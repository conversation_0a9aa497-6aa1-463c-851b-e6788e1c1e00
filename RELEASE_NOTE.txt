Release Note:
- feat: 設定頁面狀態管理與日誌增強
- feat: 豐富使用者編輯頁面資訊並調整使用者列表
- refactor: 移除 UserProfileUnifiedService 中的用戶創建和初始化邏輯
- feat: 整合設備信息追蹤並優化用戶認證流程
- chore: 移除舊的 public/vision.html 並更新 website/vision.html 標題
- feat: 整合日誌管理頁面並調整設置頁面佈局
- feat: 引入統一 URL 啟動服務 `UrlLauncherService`
- refactor: 將 `ChartPage` 重構為使用 `ChartDisplayWidget`
- feat: 頁面內容添加響應式寬度限制
- feat: 為主要頁面添加響應式佈局
- feat: 為多個頁面添加響應式佈局包裝器
- feat: 調整占星模式預設行星顯示及星盤繪製
- feat: 全面優化響應式 UI，提升大螢幕體驗
- fix: 更新 macOS 應用程式識別碼並調整 Firebase 配置
- refactor: 註解小限法推運卡片中「圈內累計」度數顯示
- fix: 調整太陽返照盤計算精度與迭代次數
- feat: 新增星座背景色顯示選項
- refactor: 註解卜卦分析頁面頂部 TabBar
- feat: 優化 AI 解讀指引並支持流式內容複製與分享
- feat: 為 AI 分析引入獨立的星體顯示和相位容許度配置
