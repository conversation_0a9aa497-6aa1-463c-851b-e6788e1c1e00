import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

import '../../data/services/api/ai_api_service.dart';
import '../../data/services/api/firebase_auth_service.dart';
import '../../data/services/api/log_management_service.dart';
import '../../data/services/api/remote_config_service.dart';
import '../../data/services/notification/notification_service.dart';
import '../../firebase_options.dart';
import '../../shared/utils/device_info_service.dart';
import '../utils/logger_utils.dart';
import '../utils/persistent_logger.dart';
import 'navigation_service.dart';

/// 應用程式初始化服務
/// 負責應用程式啟動時的各種初始化工作
class AppInitializationService {
  static bool _initialized = false;
  
  /// 初始化應用程式（快速啟動版本）
  static Future<void> initialize() async {
    if (_initialized) return;

    try {
      // 先使用 print 避免在日誌系統初始化前使用 logger
      print('開始快速初始化應用程式...');

      // 1. 首先初始化日誌系統（必須在其他服務之前）
      await _initializeLogging();

      // 2. 設置全域錯誤處理（不依賴其他服務）
      _setupGlobalErrorHandling();

      // 3. 標記為已初始化，允許應用啟動
      _initialized = true;
      logger.i('應用程式快速初始化完成');

      // 4. 初始化設備信息服務
      await DeviceInfoService.initialize();

      // 5. 在背景初始化其他服務（不阻塞啟動）
      _initializeBackgroundServices();

    } catch (e, stackTrace) {
      logger.e('應用程式初始化失敗: $e', e, stackTrace);

      // 記錄崩潰報告
      try {
        await LogManagementService.instance.logCrashReport(
          error: '應用程式初始化失敗: $e',
          stackTrace: stackTrace,
          additionalInfo: {
            'initialization_step': '應用程式初始化',
            'platform': kIsWeb ? 'web' : 'mobile',
          },
        );
      } catch (logError) {
        print('記錄崩潰報告失敗: $logError');
      }

      rethrow;
    }
  }

  /// 在背景初始化其他服務
  static void _initializeBackgroundServices() {
    // 使用 Future.microtask 確保不阻塞當前執行
    Future.microtask(() async {
      try {
        logger.i('開始背景服務初始化...');

        // Firebase 初始化
        await _initializeFirebase();

        // 其他服務初始化
        await _initializeOtherServices();

        logger.i('背景服務初始化完成');
      } catch (e) {
        logger.e('背景服務初始化失敗: $e');
        // 背景服務失敗不應該影響應用運行
      }
    });
  }
  
  /// 初始化 Firebase
  static Future<void> _initializeFirebase() async {
    try {
      logger.i('開始初始化 Firebase...');

      // 檢查是否已經初始化
      try {
        final apps = Firebase.apps;
        if (apps.isNotEmpty) {
          logger.i('Firebase 已經初始化，應用數量: ${apps.length}');
          // 即使已初始化，也要確保相關服務已啟動
          await _initializeFirebaseServices();
          return;
        }
      } catch (e) {
        logger.w('檢查 Firebase 應用狀態失敗: $e');
      }

      // 執行 Firebase 初始化
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      // 驗證初始化結果
      List<FirebaseApp> apps = Firebase.apps;
      final hasDefaultApp = apps.any((app) => app.name == '[DEFAULT]');

      if (hasDefaultApp) {
        logger.i('Firebase 初始化成功，默認應用已創建');
        await _initializeFirebaseServices();
      } else {
        logger.e('Firebase 初始化失敗：未找到默認應用');
      }
    } catch (e) {
      logger.e('Firebase 初始化失敗: $e');
      logger.e('錯誤類型: ${e.runtimeType}');

      // 檢查是否是配置文件問題
      if (e.toString().contains('google-services.json') ||
          e.toString().contains('GoogleService-Info.plist')) {
        logger.e('可能是 Firebase 配置文件問題，請檢查：');
        logger.e('  - Android: app/google-services.json');
        logger.e('  - iOS: ios/Runner/GoogleService-Info.plist');
      }

      // Firebase 初始化失敗不應該阻止應用程式啟動
      // 只是某些功能可能無法使用
    }
  }

  /// 初始化 Firebase 相關服務
  static Future<void> _initializeFirebaseServices() async {
    // 初始化 Firebase App Check
    try {
      await _initializeAppCheck();
      logger.i('Firebase App Check 初始化成功');
    } catch (e) {
      logger.e('Firebase App Check 初始化失敗: $e');
    }

    // 初始化 Firebase 認證服務
    try {
      await FirebaseAuthService.initialize();
      logger.i('Firebase 認證服務初始化成功');
    } catch (e) {
      logger.e('Firebase 認證服務初始化失敗: $e');
    }

    // 初始化 Remote Config 服務
    try {
      await RemoteConfigService.initialize();
      logger.i('Remote Config 服務初始化成功');

      // 讀取並設定 AI API Keys
      await _loadAndSetAIApiKeys();
    } catch (e) {
      logger.e('Remote Config 服務初始化失敗: $e');
    }

    // 初始化通知服務（確保在 Firebase 初始化後）
    try {
      // 延遲一點確保 Firebase 完全初始化
      await Future.delayed(const Duration(milliseconds: 500));

      // 初始化通知服務
      await NotificationService.initialize(
        onNotificationReceived: (notification) {
          logger.i('收到通知: ${notification.title}');
          // 在前景顯示通知 SnackBar
          NavigationService.showNotificationSnackBar(
            title: notification.title,
            body: notification.body,
            onTap: () => NavigationService.handleNotificationNavigation(
              notificationId: notification.id,
              actionUrl: notification.actionUrl,
              data: notification.data,
            ),
          );
        },
        onNotificationTapped: (notificationId) {
          logger.i('通知被點擊: $notificationId');
          // 導航到通知中心
          NavigationService.navigateToNotificationCenter(
            notificationId: notificationId,
          );
        },
      );
      logger.i('通知服務初始化成功');
    } catch (e) {
      logger.e('通知服務初始化失敗: $e');
    }
  }

  /// 初始化 Firebase App Check
  static Future<void> _initializeAppCheck() async {
    try {
      if (kIsWeb) {
        // Web 平台使用 ReCaptcha
        await FirebaseAppCheck.instance.activate(
          webProvider: ReCaptchaV3Provider('recaptcha-v3-site-key'),
        );
      } else {
        // 移動平台使用設備檢查
        await FirebaseAppCheck.instance.activate(
          androidProvider: AndroidProvider.debug,
          appleProvider: AppleProvider.debug,
        );
      }
      logger.i('Firebase App Check 激活成功');
    } catch (e) {
      logger.e('Firebase App Check 激活失敗: $e');
      // App Check 失敗不應該阻止應用程式運行
    }
  }

  /// 讀取並設定 AI API Keys
  static Future<void> _loadAndSetAIApiKeys() async {
    try {
      logger.i('🔑 開始讀取並設定 AI API Keys...');

      // 獲取所有 API Keys
      final openAIKey = RemoteConfigService.getOpenAIKey();
      final groqKey = RemoteConfigService.getGroqAIKey();
      final geminiKey = RemoteConfigService.getGoogleGeminiKey();

      logger.i('API Keys 獲取結果:');
      logger.i('  - OpenAI: ${openAIKey.isNotEmpty ? "✅ 已獲取 (長度: ${openAIKey.length})" : "❌ 未獲取"}');
      logger.i('  - Groq: ${groqKey.isNotEmpty ? "✅ 已獲取 (長度: ${groqKey.length})" : "❌ 未獲取"}');
      logger.i('  - Gemini: ${geminiKey.isNotEmpty ? "✅ 已獲取 (長度: ${geminiKey.length})" : "❌ 未獲取"}');

      // 設定 API Keys 到服務中
      int setCount = 0;

      if (openAIKey.isNotEmpty) {
        await AIApiService.setOpenAIApiKey(openAIKey);
        setCount++;
        logger.i('✅ OpenAI API Key 已設定');
      }

      if (groqKey.isNotEmpty) {
        await AIApiService.setGroqApiKey(groqKey);
        setCount++;
        logger.i('✅ Groq API Key 已設定');
      }

      if (geminiKey.isNotEmpty) {
        await AIApiService.setGeminiApiKey(geminiKey);
        setCount++;
        logger.i('✅ Gemini API Key 已設定');
      }

      if (setCount > 0) {
        logger.i('🎉 成功設定 $setCount 個 AI API Keys 到服務中');

        // 驗證設定是否成功
        final openAIConfigured =
            await AIApiService.isApiKeyConfigured(AIProvider.openai);
        final groqConfigured =
            await AIApiService.isApiKeyConfigured(AIProvider.groq);
        final geminiConfigured =
            await AIApiService.isApiKeyConfigured(AIProvider.gemini);

        logger.i('API Keys 配置驗證:');
        logger.i('  - OpenAI: ${openAIConfigured ? "✅ 已配置" : "❌ 未配置"}');
        logger.i('  - Groq: ${groqConfigured ? "✅ 已配置" : "❌ 未配置"}');
        logger.i('  - Gemini: ${geminiConfigured ? "✅ 已配置" : "❌ 未配置"}');
      } else {
        logger.w('⚠️ 所有 AI API Keys 都未設定，AI 解讀功能將無法使用');
        logger.w('請在 Firebase Remote Config 中配置 ai_api_keys 參數');
      }
    } catch (e) {
      logger.e('❌ 讀取或設定 AI API Keys 失敗: $e');
      logger.e('錯誤堆疊: ${StackTrace.current}');
    }
  }

  /// 初始化日誌系統
  static Future<void> _initializeLogging() async {
    try {
      // 先使用 print，避免在 PersistentLogger 初始化前使用 logger
      print('初始化日誌系統...');

      // 初始化持久化日誌器
      await PersistentLogger.instance.initialize();

      // 現在可以安全使用 logger
      logger.i('PersistentLogger 初始化成功');

      // 初始化日誌管理服務
      await LogManagementService.instance.initialize();

      logger.i('日誌系統初始化完成');

    } catch (e) {
      print('日誌系統初始化失敗: $e');
      // 日誌系統初始化失敗不應該阻止應用程式啟動
    }
  }
  
  /// 設置全域錯誤處理
  static void _setupGlobalErrorHandling() {
    // Flutter 框架錯誤處理
    FlutterError.onError = (FlutterErrorDetails details) {
      logger.e(
        'Flutter 框架錯誤: ${details.exception}',
        details.exception,
        details.stack,
      );
      
      // 記錄崩潰報告
      LogManagementService.instance.logCrashReport(
        error: 'Flutter 框架錯誤: ${details.exception}',
        stackTrace: details.stack ?? StackTrace.current,
        additionalInfo: {
          'error_type': 'flutter_framework_error',
          'library': details.library,
          'context': details.context?.toString(),
        },
      );
    };
    
    // 平台錯誤處理（僅非 Web 平台）
    if (!kIsWeb) {
      PlatformDispatcher.instance.onError = (error, stack) {
        logger.e('平台錯誤: $error', error, stack);
        
        // 記錄崩潰報告
        LogManagementService.instance.logCrashReport(
          error: '平台錯誤: $error',
          stackTrace: stack,
          additionalInfo: {
            'error_type': 'platform_error',
          },
        );
        
        return true;
      };
    }
    
    logger.i('全域錯誤處理已設置');
  }
  
  /// 初始化其他服務
  static Future<void> _initializeOtherServices() async {
    try {
      logger.i('初始化其他服務...');
      
      // 這裡可以添加其他需要初始化的服務
      // 例如：
      // - 用戶偏好設定服務
      // - 網路狀態監控服務
      // - 推送通知服務
      // - 分析服務等
      
      logger.i('其他服務初始化完成');
      
    } catch (e) {
      logger.e('其他服務初始化失敗: $e');
      // 根據具體服務決定是否需要拋出異常
    }
  }
  
  /// 應用程式關閉時的清理工作
  static Future<void> dispose() async {
    try {
      logger.i('開始應用程式清理工作...');
      
      // 確保日誌被保存
      await PersistentLogger.instance.dispose();
      
      logger.i('應用程式清理工作完成');
      
    } catch (e) {
      print('應用程式清理工作失敗: $e');
    }
  }
  
  /// 檢查初始化狀態
  static bool get isInitialized => _initialized;
}

/// 性能監控工具
class PerformanceMonitor {
  static final Map<String, Stopwatch> _stopwatches = {};
  
  /// 開始性能監控
  static void start(String operation) {
    _stopwatches[operation] = Stopwatch()..start();
  }
  
  /// 結束性能監控並記錄
  static void end(String operation, {Map<String, dynamic>? metadata}) {
    final stopwatch = _stopwatches.remove(operation);
    if (stopwatch != null) {
      stopwatch.stop();
      
      // 記錄性能日誌
      LogManagementService.instance.logPerformance(
        operation: operation,
        duration: stopwatch.elapsed,
        metadata: metadata,
      );
      
      logger.d('性能監控: $operation 耗時 ${stopwatch.elapsedMilliseconds}ms');
    }
  }
  
  /// 監控異步操作的性能
  static Future<T> monitor<T>(
    String operation,
    Future<T> Function() function, {
    Map<String, dynamic>? metadata,
  }) async {
    start(operation);
    try {
      final result = await function();
      end(operation, metadata: metadata);
      return result;
    } catch (e) {
      end(operation, metadata: {
        ...?metadata,
        'error': e.toString(),
        'success': false,
      });
      rethrow;
    }
  }
  
  /// 監控同步操作的性能
  static T monitorSync<T>(
    String operation,
    T Function() function, {
    Map<String, dynamic>? metadata,
  }) {
    start(operation);
    try {
      final result = function();
      end(operation, metadata: metadata);
      return result;
    } catch (e) {
      end(operation, metadata: {
        ...?metadata,
        'error': e.toString(),
        'success': false,
      });
      rethrow;
    }
  }
}

/// 應用程式生命週期監聽器
class AstrealLifecycleObserver extends WidgetsBindingObserver {
  static AstrealLifecycleObserver? _instance;
  static AstrealLifecycleObserver get instance => _instance ??= AstrealLifecycleObserver._();
  
  AstrealLifecycleObserver._();
  
  /// 註冊生命週期監聽
  void register() {
    WidgetsBinding.instance.addObserver(this);
    logger.i('應用程式生命週期監聽器已註冊');
  }
  
  /// 取消註冊生命週期監聽
  void unregister() {
    WidgetsBinding.instance.removeObserver(this);
    logger.i('應用程式生命週期監聽器已取消註冊');
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    logger.i('應用程式生命週期狀態變更: ${state.name}');
    
    switch (state) {
      case AppLifecycleState.resumed:
        _onAppResumed();
        break;
      case AppLifecycleState.paused:
        _onAppPaused();
        break;
      case AppLifecycleState.detached:
        _onAppDetached();
        break;
      case AppLifecycleState.inactive:
        _onAppInactive();
        break;
      case AppLifecycleState.hidden:
        _onAppHidden();
        break;
    }
  }
  
  void _onAppResumed() {
    logger.d('應用程式恢復前台');
    // 可以在這裡執行應用程式恢復時的邏輯
  }
  
  void _onAppPaused() {
    logger.d('應用程式進入後台');
    // 可以在這裡執行應用程式暫停時的邏輯
    // 例如保存狀態、暫停定時器等
  }
  
  void _onAppDetached() {
    logger.d('應用程式即將終止');
    // 執行清理工作
    AppInitializationService.dispose();
  }
  
  void _onAppInactive() {
    logger.d('應用程式變為非活動狀態');
  }
  
  void _onAppHidden() {
    logger.d('應用程式被隱藏');
  }
}
