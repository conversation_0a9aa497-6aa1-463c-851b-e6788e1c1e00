import 'package:flutter/material.dart';

import 'astrology_constants.dart';

/// 占星符號工具類
/// 提供所有占星學中使用的符號及其中文說明
class ZodiacSymbols {
  // 星座符號
  static const Map<String, Map<String, dynamic>> ZODIAC_SIGNS = {
    'ARIES': {'symbol': '♈︎', 'name': '牡羊座', 'description': '活力、衝勁、領導力'},
    'TAURUS': {'symbol': '♉︎', 'name': '金牛座', 'description': '穩定、實際、感官享受'},
    'GEMINI': {'symbol': '♊︎', 'name': '雙子座', 'description': '溝通、好奇、靈活多變'},
    'CANCER': {'symbol': '♋︎', 'name': '巨蟹座', 'description': '情感、保護、直覺'},
    'LEO': {'symbol': '♌︎', 'name': '獅子座', 'description': '創造力、自信、表現力'},
    'VIRGO': {'symbol': '♍︎', 'name': '處女座', 'description': '分析、實用、完美主義'},
    'LIBRA': {'symbol': '♎︎', 'name': '天秤座', 'description': '平衡、和諧、關係'},
    'SCORPIO': {'symbol': '♏︎', 'name': '天蠍座', 'description': '深度、轉化、神秘'},
    'SAGITTARIUS': {'symbol': '♐︎', 'name': '射手座', 'description': '冒險、自由、哲學'},
    'CAPRICORN': {'symbol': '♑︎', 'name': '摩羯座', 'description': '野心、紀律、責任'},
    'AQUARIUS': {'symbol': '♒︎', 'name': '水瓶座', 'description': '創新、獨立、人道主義'},
    'PISCES': {'symbol': '♓︎', 'name': '雙魚座', 'description': '靈性、同理心、夢想'},
  };

  // 星座符號表 (簡化版，用於快速查詢)
  static const Map<String, String> ZODIAC_SYMBOLS = {
    '牡羊座': '♈',
    '金牛座': '♉',
    '雙子座': '♊',
    '巨蟹座': '♋',
    '獅子座': '♌',
    '處女座': '♍',
    '天秤座': '♎',
    '天蠍座': '♏',
    '射手座': '♐',
    '摩羯座': '♑',
    '水瓶座': '♒',
    '雙魚座': '♓',
  };

  // 行星符號 ⯓ ♇
  static const Map<String, Map<String, dynamic>> PLANETS = {
    'SUN': {
      'symbol': '☉',
      'name': '太陽',
      'description': '核心自我、生命力、意識',
      'color': Colors.red,
    },
    'MOON': {
      'symbol': '☽',
      'name': '月亮',
      'description': '情感、潛意識、本能反應',
      'color': Color(0xFF0A0AFD),
    },
    'MERCURY': {
      'symbol': '☿',
      'name': '水星',
      'description': '思維、溝通、學習能力',
      'color': Color(0xFF00673F),
    },
    'VENUS': {
      'symbol': '♀',
      'name': '金星',
      'description': '愛情、美感、價值觀',
      'color': Color(0xFFCA9833),
    },
    'MARS': {
      'symbol': '♂',
      'name': '火星',
      'description': '行動力、慾望、競爭力',
      'color': Colors.red
    },
    'JUPITER': {
      'symbol': '♃',
      'name': '木星',
      'description': '擴張、幸運、信念',
      'color': Colors.red,
    },
    'SATURN': {
      'symbol': '♄',
      'name': '土星',
      'description': '限制、責任、紀律',
      'color': Color(0xFFCA9833),
    },
    'URANUS': {
      'symbol': '♅',
      'name': '天王星',
      'description': '變革、獨創、突破',
      'color': Color(0xFF00673F),
    },
    'NEPTUNE': {
      'symbol': '♆',
      'name': '海王星',
      'description': '靈性、幻想、溶解',
      'color': Color(0xFF0A34FD),
    },
    'PLUTO': {
      'symbol': '⯓',
      'name': '冥王星',
      'description': '轉化、權力、重生',
      'color': Color(0xFF0A0AFD),
    },
    'NORTH_NODE': {
      'symbol': '☊',
      'name': '北交點',
      'description': '命運方向、靈魂成長',
      'color': Color(0xFF00B7B7),
    },
    'SOUTH_NODE': {
      'symbol': '☋',
      'name': '南交點',
      'description': '過去業力、舒適區',
      'color': Color(0xFF00B7B7),
    },
    'CHIRON': {
      'symbol': '⚷',
      'name': '凱龍星',
      'description': '內在傷痛、療癒能力',
      'color': Color(0xFFFD33FD),
    },
    'CERES': {
      'symbol': '⚳',
      'name': '穀神星',
      'description': '滋養、照顧、豐收',
      'color': Color(0xFFFD33FD),
    },
    'PALLAS': {
      'symbol': '⚴',
      'name': '智神星',
      'description': '智慧、策略、創造力',
      'color': Color(0xFFFD33FD),
    },
    'JUNO': {
      'symbol': '⚵',
      'name': '婚神星',
      'description': '承諾、婚姻、伴侶關係',
      'color': Color(0xFFFD33FD),
    },
    'VESTA': {
      'symbol': '⚶',
      'name': '灶神星',
      'description': '奉獻、專注、神聖空間',
      'color': Color(0xFFFD33FD),
    },
    'LILITH': {
      'symbol': '⚸',
      'name': '莉莉絲',
      'description': '原始力量、拒絕從屬、陰影面',
      'color': Color(0xFFFD33FD),
    },
  };

  // 行星符號表 (簡化版，用於快速查詢)
  static const Map<String, String> PLANET_SYMBOLS = {
    '太陽': '☉',
    '月亮': '☽',
    '水星': '☿',
    '金星': '♀',
    '火星': '♂',
    '木星': '♃',
    '土星': '♄',
    '天王星': '♅',
    '海王星': '♆',
    '冥王星': '⯓',
    '北交點': '☊',
    '南交點': '☋',
  };

  // 相位符號
  static const Map<String, Map<String, dynamic>> ASPECTS = {
    'CONJUNCTION': {
      'symbol': '☌',
      'name': '合相',
      'description': '融合、結合、強化',
      'angle': 0.0,
      'orb': 8.0,
      'color': Colors.red
    },
    'OPPOSITION': {
      'symbol': '☍',
      'name': '對分相',
      'description': '對立、平衡、覺察',
      'angle': 180.0,
      'orb': 8.0,
      'color': Colors.blue
    },
    'TRINE': {
      'symbol': '△',
      'name': '三分相',
      'description': '和諧、流暢、天賦',
      'angle': 120.0,
      'orb': 8.0,
      'color': Colors.green
    },
    'SQUARE': {
      'symbol': '□',
      'name': '四分相',
      'description': '緊張、挑戰、行動',
      'angle': 90.0,
      'orb': 8.0,
      'color': Colors.red
    },
    'SEXTILE': {
      'symbol': '⚹',
      'name': '六分相',
      'description': '機會、支持、成長',
      'angle': 60.0,
      'orb': 4.0,
      'color': Colors.green
    },
    'QUINCUNX': {
      'symbol': '⚻',
      'name': '五十分相',
      'description': '調整、不協調、轉變',
      'angle': 150.0,
      'orb': 3.0,
      'color': Colors.grey
    },
    'SEMISEXTILE': {
      'symbol': '⚺',
      'name': '三十分相',
      'description': '輕微連接、潛在發展',
      'angle': 30.0,
      'orb': 2.0,
      'color': Colors.grey
    },
    'SEMISQUARE': {
      'symbol': '⚼',
      'name': '四十五分相',
      'description': '輕微緊張、內在衝突',
      'angle': 45.0,
      'orb': 2.0,
      'color': Colors.grey
    },
    'SESQUISQUARE': {
      'symbol': '⚿',
      'name': '一百三十五分相',
      'description': '內在調整、壓力點',
      'angle': 135.0,
      'orb': 2.0,
      'color': Colors.grey
    },
    'QUINTILE': {
      'symbol': 'Q',
      'name': '七十二分相',
      'description': '創造力、才能表現',
      'angle': 72.0,
      'orb': 2.0,
      'color': Colors.grey
    },
    'BIQUINTILE': {
      'symbol': 'bQ',
      'name': '一百四十四分相',
      'description': '創意表達、獨特視角',
      'angle': 144.0,
      'orb': 2.0,
      'color': Colors.grey
    },
  };

  // 相位符號表 (簡化版，用於快速查詢)
  static const Map<String, String> ASPECT_SYMBOLS = {
    '合相': '☌',
    '六分相': '⚹',
    '四分相': '□',
    '三分相': '△',
    '對分相': '☍',
  };

  // 相位顏色表 (簡化版，用於快速查詢)
  static const Map<String, Color> ASPECT_COLORS = {
    '☌': Colors.red, // 合相
    '⚹': Colors.green, // 六分相
    '□': Colors.red, // 四分相
    '△': Colors.green, // 三分相
    '☍': Colors.blue, // 對分相
  };

  // 其他符號
  static const Map<String, Map<String, dynamic>> OTHER_SYMBOLS = {
    'ASCENDANT': {
      'symbol': 'ASC',
      'name': '上升點',
      'description': '自我表達、外在形象、人格面具'
    },
    'MIDHEAVEN': {'symbol': 'MC', 'name': '天頂', 'description': '事業、社會地位、公眾形象'},
    'DESCENDANT': {
      'symbol': 'DSC',
      'name': '下降點',
      'description': '人際關係、伴侶、他人投射'
    },
    'IMUM_COELI': {'symbol': 'IC', 'name': '天底', 'description': '家庭、根源、內在安全感'},
    'PART_OF_FORTUNE': {
      'symbol': '⊗',
      'name': '福點',
      'description': '幸福、機遇、天賦才能'
    },
    'VERTEX': {'symbol': 'Vx', 'name': '命運點', 'description': '命運相遇、重要關係'},
  };

  // 宮位說明
  static const Map<String, Map<String, dynamic>> HOUSES = {
    'HOUSE1': {'name': '第一宮', 'description': '自我、外表、個性、生命力'},
    'HOUSE2': {'name': '第二宮', 'description': '財產、價值觀、資源、安全感'},
    'HOUSE3': {'name': '第三宮', 'description': '溝通、短途旅行、兄弟姐妹、早期教育'},
    'HOUSE4': {'name': '第四宮', 'description': '家庭、根源、父母、內在情感基礎'},
    'HOUSE5': {'name': '第五宮', 'description': '創造力、娛樂、子女、浪漫關係'},
    'HOUSE6': {'name': '第六宮', 'description': '健康、工作、日常生活、服務'},
    'HOUSE7': {'name': '第七宮', 'description': '關係、婚姻、合作夥伴、公開的敵人'},
    'HOUSE8': {'name': '第八宮', 'description': '共享資源、轉變、性、死亡與重生'},
    'HOUSE9': {'name': '第九宮', 'description': '高等教育、哲學、長途旅行、信仰'},
    'HOUSE10': {'name': '第十宮', 'description': '職業、社會地位、名聲、權威'},
    'HOUSE11': {'name': '第十一宮', 'description': '友誼、團體、願望、社會理想'},
    'HOUSE12': {'name': '第十二宮', 'description': '潛意識、秘密、限制、靈性成長'},
  };

  /// 獲取所有星座符號
  static List<String> getAllZodiacSymbols() {
    return ZODIAC_SYMBOLS.values.toList();
  }

  /// 獲取星座符號
  static String getZodiacSymbol(String sign) {
    return ZODIAC_SYMBOLS[sign] ?? '?';
  }

  /// 獲取行星符號（從 AstrologyConstants.PLANETS 中獲取）
  static String getPlanetSymbol(String planet) {
    try {
      // 從 AstrologyConstants.PLANETS 中查找對應的行星
      final planetData = AstrologyConstants.PLANETS.firstWhere(
        (p) => p['name'] == planet,
      );
      return planetData['symbol'] as String;
    } catch (e) {
      // 如果找不到，回退到原有的 PLANET_SYMBOLS
      return PLANET_SYMBOLS[planet] ?? '?';
    }
  }

  /// 獲取相位符號
  static String getAspectSymbol(String aspect) {
    return ASPECT_SYMBOLS[aspect] ?? '?';
  }

  /// 獲取相位顏色
  static Color getAspectColor(String symbol) {
    return ASPECT_COLORS[symbol] ?? Colors.grey;
  }

  // 獲取所有行星符號列表
  static List<String> getAllPlanetSymbols() {
    return PLANET_SYMBOLS.values.toList();
  }

  // 根據符號獲取中文名稱
  static String getNameBySymbol(String symbol) {
    // 檢查星座
    for (var entry in ZODIAC_SYMBOLS.entries) {
      if (entry.value == symbol) return entry.key;
    }

    // 檢查行星
    for (var entry in PLANET_SYMBOLS.entries) {
      if (entry.value == symbol) return entry.key;
    }

    // 檢查其他符號
    for (var other in OTHER_SYMBOLS.values) {
      if (other['symbol'] == symbol) return other['name'] as String;
    }

    return '未知符號';
  }

  // 根據符號獲取描述
  static String getDescriptionBySymbol(String symbol) {
    // 檢查星座
    for (var sign in ZODIAC_SIGNS.values) {
      if (sign['symbol'] == symbol) return sign['description'] as String;
    }

    // 檢查行星
    for (var planet in PLANETS.values) {
      if (planet['symbol'] == symbol) return planet['description'] as String;
    }

    // 檢查相位
    for (var aspect in ASPECTS.values) {
      if (aspect['symbol'] == symbol) return aspect['description'] as String;
    }

    // 檢查其他符號
    for (var other in OTHER_SYMBOLS.values) {
      if (other['symbol'] == symbol) return other['description'] as String;
    }

    return '無描述';
  }

  // 獲取星座顏色
  static Color getZodiacColor(String zodiacName) {
    final Map<String, Color> zodiacColors = {
      // 火象星座 - 紅色
      // 土象星座 - 黃色
      // 風象星座 - 綠色
      // 水象星座 - 藍色
      '牡羊座': Colors.red,
      '金牛座': Color(0xFFCA9833),
      '雙子座': Color(0xFF00673F),
      '巨蟹座': Color(0xFF0A0AFD),
      '獅子座': Colors.red,
      '處女座': Color(0xFFCA9833),
      '天秤座': Color(0xFF00673F),
      '天蠍座': Color(0xFF0A0AFD),
      '射手座': Colors.red,
      '摩羯座': Color(0xFFCA9833),
      '水瓶座': Color(0xFF00673F),
      '雙魚座': Color(0xFF0A0AFD),
    };
    return zodiacColors[zodiacName] ?? Colors.black;
  }
}
