import 'package:intl/intl.dart';
import 'package:sweph/sweph.dart';

import '../astrology/aspect_info.dart';
import '../astrology/chart_category.dart';
import '../astrology/planet_position.dart';
import 'gender.dart';

class BirthData {
  final String id;
  final String name;
  final DateTime dateTime;
  final String birthPlace;
  final String? notes;
  final double latitude;
  final double longitude;
  final DateTime createdAt; // 添加建立時間字段
  final ChartCategory category; // 添加類別字段
  final Gender? gender; // 性別字段（非必填）
  final DateTime? lastAccessedAt; // 最後訪問時間
  final bool isTimeUncertain; // 出生時間是否不確定

  BirthData({
    required this.id,
    required this.name,
    required this.dateTime,
    required this.birthPlace,
    this.notes,
    required this.latitude,
    required this.longitude,
    DateTime? createdAt,
    this.category = ChartCategory.personal, // 默認類別為個人
    this.gender, // 性別（非必填）
    this.lastAccessedAt,
    this.isTimeUncertain = false, // 默認出生時間確定
  }) :
    createdAt = createdAt ?? DateTime.now();

  List<PlanetPosition>? planets;
  HouseCuspData? houses;
  List<AspectInfo>? aspects;

  // 從 JSON 創建 BirthData
  factory BirthData.fromJson(Map<String, dynamic> json) {
    return BirthData(
      id: json['id'] as String,
      name: json['name'] as String,
      dateTime: DateTime.parse(json['birthDate'] as String),
      birthPlace: json['birthPlace'] as String,
      notes: json['notes'] as String?,
      latitude: json['latitude'] as double,
      longitude: json['longitude'] as double,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt'] as String) : null,
      category: ChartCategory.fromString(json['category'] as String? ?? '個人'),
      gender: json['gender'] != null ? Gender.fromString(json['gender'] as String) : null,
      lastAccessedAt: json['lastAccessedAt'] != null ? DateTime.parse(json['lastAccessedAt'] as String) : null,
      isTimeUncertain: json['isTimeUncertain'] as bool? ?? false,
    );
  }

  // 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'birthDate': DateFormat('yyyy-MM-dd HH:mm').format(dateTime),
      'birthPlace': birthPlace,
      'notes': notes,
      'latitude': latitude,
      'longitude': longitude,
      'createdAt': DateFormat('yyyy-MM-dd HH:mm:ss').format(createdAt),
      'category': category.toStorageString(),
      'gender': gender?.toStorageString(),
      'lastAccessedAt': lastAccessedAt?.toIso8601String(),
      'isTimeUncertain': isTimeUncertain,
    };
  }

  // 創建副本並更新部分屬性
  BirthData copyWith({
    String? id,
    String? name,
    DateTime? birthDate,
    String? birthPlace,
    String? notes,
    double? latitude,
    double? longitude,
    DateTime? createdAt,
    ChartCategory? category,
    Gender? gender,
    String? folderId,
    List<String>? tags,
    bool? isFavorite,
    DateTime? lastAccessedAt,
    bool? isTimeUncertain,
  }) {
    return BirthData(
      id: id ?? this.id,
      name: name ?? this.name,
      dateTime: birthDate ?? this.dateTime,
      birthPlace: birthPlace ?? this.birthPlace,
      notes: notes ?? this.notes,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      createdAt: createdAt ?? this.createdAt,
      category: category ?? this.category,
      gender: gender ?? this.gender,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
      isTimeUncertain: isTimeUncertain ?? this.isTimeUncertain,
    );
  }
}
