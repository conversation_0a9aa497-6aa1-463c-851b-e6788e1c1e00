import 'package:cloud_firestore/cloud_firestore.dart';

/// 用戶檔案模型
/// 對應 Firestore 中的 user_profiles 集合
class UserProfile {
  final String userId;
  final String? email;
  final String displayName;
  final bool isAnonymous;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool profileCompleted;
  final DateTime lastLoginAt;
  final int loginCount;
  final int interpretationCredits; // 可用解讀次數
  final DateTime creditsLastUpdated;
  final String? photoURL;
  final bool? emailVerified;
  final bool? isAdmin;
  final String? lastLoginIp; // 最後登入 IP 地址
  final String? platform; // 平台信息 (web, android, ios, etc.)

  const UserProfile({
    required this.userId,
    this.email,
    required this.displayName,
    this.isAnonymous = false,
    required this.createdAt,
    required this.updatedAt,
    this.profileCompleted = false,
    required this.lastLoginAt,
    this.loginCount = 1,
    this.interpretationCredits = 0,
    required this.creditsLastUpdated,
    this.photoURL,
    this.emailVerified,
    this.isAdmin,
    this.lastLoginIp,
    this.platform,
  });

  /// 從 JSON 創建 UserProfile
  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      userId: json['user_id'] as String,
      email: json['email'] as String?,
      displayName: json['display_name'] as String? ??
          json['displayName'] as String? ??
          (json['is_anonymous'] == true ? '匿名用戶' : '新用戶'),
      isAnonymous: json['is_anonymous'] as bool? ??
          json['isAnonymous'] as bool? ??
          false,
      createdAt: _parseDateTime(json['created_at']),
      updatedAt: json['updated_at'].toDate(),
      profileCompleted: json['profile_completed'] as bool? ??
          json['profileCompleted'] as bool? ??
          false,
      lastLoginAt: _parseDateTime(json['last_login_at']),
      loginCount:
          json['login_count'] as int? ?? json['loginCount'] as int? ?? 1,
      interpretationCredits: json['interpretation_credits'] as int? ??
          json['interpretationCredits'] as int? ??
          0,
      creditsLastUpdated: _parseDateTime(json['credits_last_updated']),
      photoURL: json['photo_url'] as String? ?? json['photoURL'] as String?,
      emailVerified:
          json['email_verified'] as bool? ?? json['emailVerified'] as bool?,
      isAdmin: json['is_admin'] as bool? ?? json['isAdmin'] as bool? ?? false,
      lastLoginIp: json['last_login_ip'] as String? ?? json['lastLoginIp'] as String?,
      platform: json['platform'] as String?,
    );
  }

  /// 轉換為 JSON（使用 snake_case 命名，符合 Firestore 慣例）
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'email': email,
      'display_name': displayName,
      'is_anonymous': isAnonymous,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'profile_completed': profileCompleted,
      'last_login_at': lastLoginAt.toIso8601String(),
      'login_count': loginCount,
      'interpretation_credits': interpretationCredits,
      'credits_last_updated': creditsLastUpdated.toIso8601String(),
      'photo_url': photoURL,
      'email_verified': emailVerified,
      'is_admin': isAdmin,
      'last_login_ip': lastLoginIp,
      'platform': platform,
    };
  }

  /// 轉換為 Firestore 格式（包含 FieldValue）
  Map<String, dynamic> toFirestoreJson({bool isUpdate = false}) {
    final data = <String, dynamic>{
      'user_id': userId,
      'email': email,
      'display_name': displayName,
      'is_anonymous': isAnonymous,
      'profile_completed': profileCompleted,
      'login_count': loginCount,
      'interpretation_credits': interpretationCredits,
      'photo_url': photoURL,
      'email_verified': emailVerified,
      'is_admin': isAdmin,
      'last_login_ip': lastLoginIp,
      'platform': platform,
    };

    if (isUpdate) {
      // 更新時使用伺服器時間戳
      data['updated_at'] = FieldValue.serverTimestamp();
      // 注意：last_login_at 不應該在一般更新時自動更新
      // 只有在明確的登入操作時才應該更新
      data['credits_last_updated'] = FieldValue.serverTimestamp();
    } else {
      // 創建時使用伺服器時間戳
      data['created_at'] = FieldValue.serverTimestamp();
      data['updated_at'] = FieldValue.serverTimestamp();
      // 創建時設定 last_login_at 為創建時間（首次登入）
      data['last_login_at'] = FieldValue.serverTimestamp();
      data['credits_last_updated'] = FieldValue.serverTimestamp();
    }

    return data;
  }

  /// 轉換為 Firestore 格式（專門用於更新登入資訊）
  Map<String, dynamic> toFirestoreJsonForLogin() {
    return {
      'last_login_at': FieldValue.serverTimestamp(),
      'login_count': loginCount + 1,
      'updated_at': FieldValue.serverTimestamp(),
    };
  }

  /// 複製並更新部分屬性
  UserProfile copyWith({
    String? userId,
    String? email,
    String? displayName,
    bool? isAnonymous,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? profileCompleted,
    DateTime? lastLoginAt,
    int? loginCount,
    int? interpretationCredits,
    DateTime? creditsLastUpdated,
    String? photoURL,
    bool? emailVerified,
    bool? isAdmin,
    String? lastLoginIp,
    String? platform,
  }) {
    return UserProfile(
      userId: userId ?? this.userId,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      profileCompleted: profileCompleted ?? this.profileCompleted,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      loginCount: loginCount ?? this.loginCount,
      interpretationCredits:
          interpretationCredits ?? this.interpretationCredits,
      creditsLastUpdated: creditsLastUpdated ?? this.creditsLastUpdated,
      photoURL: photoURL ?? this.photoURL,
      emailVerified: emailVerified ?? this.emailVerified,
      isAdmin: isAdmin ?? this.isAdmin,
      lastLoginIp: lastLoginIp ?? this.lastLoginIp,
      platform: platform ?? this.platform,
    );
  }

  /// 解析 DateTime，支援多種格式
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();

    if (value is DateTime) return value;

    if (value is Timestamp) return value.toDate();

    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        return DateTime.now();
      }
    }

    // 處理 Firestore Timestamp
    if (value is Map && value.containsKey('_seconds')) {
      final seconds = value['_seconds'] as int;
      final nanoseconds = value['_nanoseconds'] as int? ?? 0;
      return DateTime.fromMillisecondsSinceEpoch(
          seconds * 1000 + (nanoseconds / 1000000).round());
    }

    return DateTime.now();
  }

  /// 驗證用戶檔案是否有效
  bool isValid() {
    return userId.isNotEmpty && displayName.isNotEmpty;
  }

  /// 是否為新用戶（登入次數少於等於1次）
  bool get isNewUser => loginCount <= 1;

  /// 是否有可用的解讀次數
  bool get hasInterpretationCredits => interpretationCredits > 0;

  /// 獲取用戶類型描述
  String get userTypeDescription {
    if (isAnonymous) return '匿名用戶';
    if (isAdmin == true) return '管理員';
    return '註冊用戶';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserProfile && other.userId == userId;
  }

  @override
  int get hashCode => userId.hashCode;

  @override
  String toString() {
    return 'UserProfile(userId: $userId, displayName: $displayName, email: $email, '
        'isAnonymous: $isAnonymous, interpretationCredits: $interpretationCredits)';
  }
}
