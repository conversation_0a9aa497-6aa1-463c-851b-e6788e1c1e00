import '../../../core/utils/logger_utils.dart';

/// 星盤類型枚舉
enum ChartType {
  // 天象盤
  mundane('天象盤', 'Mundane Chart', '用於分析國家、社會、政治、經濟等集體層面的趨勢和變化，屬於世俗占星學範疇'),

  // 個人星盤
  natal('本命盤', 'Natal Chart', '根據出生時間、地點繪製的星盤，是占星學的基礎，反映個人的性格特質、天賦潛能、人生課題和命運走向'),

  // 預測類星盤
  transit('行運盤', 'Transit Chart', '將當前天空中行星的位置與本命盤對比，分析當前時期的機會、挑戰和發展趨勢'),
  secondaryProgression('次限推運盤', 'Secondary Progression', '以「一天等於一年」的原理，將出生後每一天的行星位置對應人生每一年，反映內在心理發展和意識演化'),
  tertiaryProgression('三限推運盤', 'Tertiary Progression', '以「一天等於一個月」的原理，分析短期內的心理狀態變化和情緒波動'),
  solarArcDirection('太陽弧推運盤', 'Solar Arc Direction', '將所有行星按照太陽的推進速度同步移動，用於預測重大的人生轉折點和關鍵事件'),

  // 返照盤類
  solarReturn('太陽返照盤', 'Solar Return', '每年太陽回到出生時相同位置的那一刻所繪製的星盤，用於分析該年度的主題、機會和挑戰'),
  lunarReturn('月亮返照盤', 'Lunar Return', '每月月亮回到出生時相同位置的那一刻所繪製的星盤，用於分析該月的情緒狀態和短期發展'),

  firdaria('法達盤', 'Firdaria Chart', '古典占星學的時序分析技術，將人生劃分為不同階段，每個階段由特定行星主導，用於了解人生各時期的主題'),
  profection('小限盤', 'Profection Chart', '古典占星學的年度預測技術，每年上升點推進一個宮位，分析該年的主題和重點領域'),

  // 合盤類
  synastry('比較盤', 'Synastry', '將兩個人的本命盤重疊比較，分析彼此行星之間的相位關係，了解兩人的相容性、互動模式和關係動力'),
  composite('組合盤', 'Composite Chart', '將兩人對應行星的中點位置計算出來，形成一個新的星盤，代表這段關係本身的特質和發展方向'),
  davison('時空中點盤', 'Davison Chart', '將兩人的出生時間和地點取平均值，在這個虛擬的時空點建立星盤，反映關係的命運和宇宙意義'),
  marks('馬克思盤', 'Marks Chart',
      '體現盤主在關係中的內心主觀感受，包括如何看待對方、對關係的情感體驗、適應程度等，是一種偏重心理層面的關係分析技術'),

  // 比較盤推運
  synastrySecondary(
      '比較次限盤', 'Synastry Secondary Progression', '將一方的次限推運盤與另一方的本命盤進行比較，觀察關係在時間推移中的長期變化'),
  synastryTertiary(
      '比較三限盤', 'Synastry Tertiary Progression', '將一方的三限推運盤與另一方的本命盤進行比較，觀察關係的短期變化和情緒波動'),

  // 組合盤推運
  compositeSecondary(
      '組合次限盤', 'Composite Secondary Progression', '對組合盤進行次限推運分析，了解關係本身的長期演化和發展趨勢'),
  compositeTertiary(
      '組合三限盤', 'Composite Tertiary Progression', '對組合盤進行三限推運分析，了解關係在短期內的變化和調整'),

  // 時空中點盤推運
  davisonSecondary(
      '時空次限盤', 'Davison Secondary Progression', '對時空中點盤進行次限推運分析，了解關係命運的長期演化'),
  davisonTertiary(
      '時空三限盤', 'Davison Tertiary Progression', '對時空中點盤進行三限推運分析，了解關係在短期內的變化'),

  // 馬克思盤推運
  marksSecondary(
      '馬克思次限盤', 'Marks Secondary Progression', '對馬克思盤進行次限推運分析，了解盤主對關係感受的長期變化'),
  marksTertiary('馬克思三限盤', 'Marks Tertiary Progression', '對馬克思盤進行三限推運分析，了解盤主對關係感受的短期變化'),

  // 事件占星
  // electional('擇日盤', 'Electional Chart', '用於挑選結婚、開業、旅行等最佳時間'),
  horary('卜卦盤', 'Horary Chart', '在提出具體問題的那一刻建立星盤，通過分析星盤來回答問題，是占星學中的問答技術'),
  event('事件盤', 'Event Chart', '為重要事件（如結婚、開業、簽約等）的發生時刻建立星盤，分析事件的意義和影響'),

  // 特殊星盤
  // fixedStars('恆星盤', 'Fixed Stars Chart', '將本命盤與恆星搭配分析，提供宿命指引'),
  // harmonic('諧波盤', 'Harmonic Chart', '利用數學運算找出隱藏在本命盤中的特定模式'),
  // draconic('龍頭盤', 'Draconic Chart', '以北交點為基準重新計算，顯示靈魂目標與業力關聯'),
  // localSpace('地平方位盤', 'Local Space Chart', '以方位角分析個人能量在不同地方的影響'),


  // 季節節氣星盤
  equinoxSolstice(
      '二分二至盤', 'Equinox & Solstice Chart', '在春分、夏至、秋分、冬至四個重要節氣時刻建立的星盤，用於分析季節能量對個人的影響'),

  // 日月蝕星盤
  eclipse('日月蝕盤', 'Eclipse Chart',
      '日月蝕被認為是強大的轉化能量，能夠帶來重大的生命轉折和集體變化，在占星學中被視為強烈的「命運扭轉點」或「集體轉化的催化劑」'),
  conjunctionJupiterSaturn('土木會合盤', 'Jupiter-Saturn Conjunction Chart',
      '象徵長期結構與集體信念的重大重組，常被視為「時代輪轉」的標誌。每20年發生一次，對社會制度、經濟潮流與文化價值觀有深遠影響，在世界占星中具有重要地位'),
  conjunctionMarsSaturn('火土會合盤', 'Mars-Saturn Conjunction Chart',
      '象徵意志與限制、行動與結構之間的緊張整合，被視為「行動的試煉」與「抗壓的開端」。通常帶來高壓情境、持久奮戰或重要責任的開啟，對個人突破與集體壓力均具高度象徵意義');

  final String displayName;
  final String englishName;
  final String description;

  const ChartType(this.displayName, this.englishName, this.description);

  // 獲取顯示名稱（中文）
  String get name => displayName;

  // 獲取英文名稱
  String get nameEn => englishName;

  // 獲取描述
  String get desc => description;

  // 根據顯示名稱獲取枚舉值
  static ChartType? fromDisplayName(String displayName) {
    return ChartType.values.firstWhere(
      (type) => type.displayName == displayName,
      orElse: () => ChartType.natal,
    );
  }

  // 根據英文名稱獲取枚舉值
  static ChartType? fromEnglishName(String englishName) {
    return ChartType.values.firstWhere(
      (type) => type.englishName == englishName,
      orElse: () => ChartType.natal,
    );
  }

  // 檢查是否為合盤類型
  bool get isRelationshipChart {
    return [
      ChartType.synastry,
      ChartType.composite,
      ChartType.davison,
      ChartType.marks,
    ].contains(this);
  }

  bool get isRelationshipPredictiveChart {
    return [
      // 比較盤推運
      ChartType.synastrySecondary,
      ChartType.synastryTertiary,

      // 組合盤推運
      ChartType.compositeSecondary,
      ChartType.compositeTertiary,

      // 時空中點盤推運
      ChartType.davisonSecondary,
      ChartType.davisonTertiary,

      // 馬克思盤推運
      ChartType.marksSecondary,
      ChartType.marksTertiary,
    ].contains(this);
  }

  // 檢查是否為預測類型
  bool get isPredictiveChart {
    return [
      ChartType.secondaryProgression,
      ChartType.tertiaryProgression,
      ChartType.solarArcDirection,
      ChartType.transit,
      ChartType.firdaria,
      // ChartType.profection,
    ].contains(this);
  }

  // 檢查是否為返照盤類型
  bool get isReturnChart {
    return [
      ChartType.solarReturn,
      ChartType.lunarReturn,
    ].contains(this);
  }

  // 檢查是否為事件類型
  bool get isEventChart {
    return [
      // ChartType.electional,
      ChartType.horary,
      ChartType.event,
    ].contains(this);
  }

  // 檢查是否為特殊類型
  bool get isSpecialChart {
    return [
      // ChartType.fixedStars,
      // ChartType.harmonic,
      // ChartType.draconic,
      // ChartType.localSpace,
      ChartType.equinoxSolstice,
      ChartType.eclipse,
      // ChartType.conjunctionJupiterSaturn,
      // ChartType.conjunctionMarsSaturn,
    ].contains(this);
  }

  // 獲取星盤類型的分類
  String get category {
    logger.d('獲取星盤類型分類: ${this.name}');

    if (this == ChartType.natal) {
      logger.d('類型: 個人星盤');
      return '個人星盤';
    }
    if (isRelationshipChart) {
      logger.d('類型: 合盤類');
      return '合盤類';
    }
    if (isPredictiveChart) {
      logger.d('類型: 預測類星盤');
      return '預測類星盤';
    }
    if (isReturnChart) {
      logger.d('類型: 返照盤類');
      return '返照盤類';
    }
    if (isEventChart) {
      logger.d('類型: 事件占星');
      return '事件占星';
    }
    if (isSpecialChart) {
      logger.d('類型: 特殊星盤');
      return '特殊星盤';
    }
    logger.d('未知的星盤類型: ${this.name}');
    return '其他';
  }

  // 檢查是否需要主要人物（天象盤、二分二至盤和日月蝕盤除外）
  bool get requiresPrimaryPerson {
    final result = this != ChartType.mundane &&
        this != ChartType.equinoxSolstice &&
        this != ChartType.eclipse;
    logger.d('檢查是否需要主要人物: ${this.name} = $result');
    return result;
  }

  // 檢查是否需要兩個人的數據
  bool get requiresTwoPersons {
    final result = isRelationshipChart || isRelationshipPredictiveChart;
    logger.d('檢查是否需要兩個人的數據: ${this.name} = $result');
    return result;
  }

  // 檢查是否需要特定日期
  bool get requiresSpecificDate {
    final result = isPredictiveChart ||
        isReturnChart ||
        isEventChart ||
        isRelationshipPredictiveChart ||
        this == ChartType.mundane ||
        this == ChartType.firdaria ||
        this == ChartType.profection;
    logger.d('檢查是否需要特定日期: ${this.name} = $result');
    return result;
  }

  // 檢查是否需要當前時間
  bool get requiresCurrentTime {
    final result = [ChartType.transit].contains(this);
    logger.d('檢查是否需要當前時間: ${this.name} = $result');
    return result;
  }

  // 檢查是否需要地點選擇
  bool get requiresLocationSelection {
    final result = [
      ChartType.mundane,
      ChartType.event,
      ChartType.horary,
      ChartType.equinoxSolstice,
      ChartType.eclipse
    ].contains(this);
    logger.d('檢查是否需要地點選擇: ${this.name} = $result');
    return result;
  }

  // 檢查是否需要年份選擇
  bool get requiresYearSelection {
    final result =
        [ChartType.equinoxSolstice, ChartType.eclipse].contains(this);
    logger.d('檢查是否需要年份選擇: ${this.name} = $result');
    return result;
  }

  // 檢查是否為組合盤推運
  bool get isCompositeProgression {
    return [
      ChartType.compositeSecondary,
      ChartType.compositeTertiary,
    ].contains(this);
  }

  // 檢查是否為比較盤推運
  bool get isSynastryProgression {
    return [
      ChartType.synastrySecondary,
      ChartType.synastryTertiary,
    ].contains(this);
  }

  // 檢查是否為時空中點盤推運
  bool get isDavisonProgression {
    return [
      ChartType.davisonSecondary,
      ChartType.davisonTertiary,
    ].contains(this);
  }

  // 檢查是否為馬克思盤推運
  bool get isMarksProgression {
    return [
      ChartType.marksSecondary,
      ChartType.marksTertiary,
    ].contains(this);
  }
}
