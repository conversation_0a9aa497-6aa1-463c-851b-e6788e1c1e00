import 'package:flutter/material.dart';

import 'aspect_info.dart';

/// 行星尊貴力量狀態枚舉
enum PlanetDignity {
  domicile, // 廟
  exaltation, // 旺
  triplicity, // 三分
  terms, // 界
  decan, // 十
  detriment, // 陷
  fall, // 弱
  peregrine, // 外來的
}

/// 行星與太陽的關係狀態枚舉
enum SolarCondition {
  cazimi, // 太陽核心（距太陽 0° 17’ 以內）
  combust, // 太陽光束傷害（距太陽 8° 以內）
  underBeams, // 太陽光束下（距太陽 8° 至 17° 之間）
  free, // 自由（距太陽 17° 以上）
}

/// 宮位類型枚舉
enum HouseType {
  angular, // 始宮（第1、第4、第7、第10宮）
  succedent, // 續宮（第2、第5、第8、第11宮）
  cadent, // 果宮（第3、第6、第9、第12宮）
}

/// 日夜區分狀態枚舉
enum SectStatus {
  inSect, // 在其區分位置
  outOfSect, // 不在其區分位置
  hayz, // （完美的區分狀態）
}

/// 行星位置模型类
class PlanetPosition {
  final int id;
  final String name;
  final String symbol;
  final double longitude;
  final double latitude;
  final double distance;
  final double longitudeSpeed;
  final double latitudeSpeed;
  final double distanceSpeed;
  final String sign;
  int house;
  final Color color; // 增加顏色屬性
  PlanetDignity dignity; // 行星尊貴力量狀態
  final SolarCondition solarCondition; // 行星與太陽的關係狀態
  final bool isDaytime; // 是否在日間區域（太陽在地平線上方）
  final HouseType houseType; // 宮位類型（始宮、續宮、果宮）
  final HouseType? wholeSignHouseType; // 整宮制下的宮位類型
  final int? wholeSignHouse; // 整宮制下的宮位數字
  final bool isPlanetDiurnal; // 行星本身的日夜屬性（日間行星或夜間行星）
  final bool isSignMasculine; // 星座的陰陽性（陽性或陰性）
  final SectStatus sectStatus; // 日夜區分狀態

  // 相位信息屬性
  List<AspectInfo> aspects = [];

  PlanetPosition({
    required this.id,
    required this.name,
    required this.symbol,
    required this.longitude,
    required this.latitude,
    required this.distance,
    required this.longitudeSpeed,
    required this.latitudeSpeed,
    required this.distanceSpeed,
    required this.sign,
    required this.house,
    this.color = Colors.grey, // 默認為灰色
    this.dignity = PlanetDignity.peregrine, // 默認為普通狀態
    this.solarCondition = SolarCondition.free, // 默認為自由狀態
    this.isDaytime = true, // 默認為日間
    this.houseType = HouseType.angular, // 默認為始宮
    this.wholeSignHouseType, // 整宮制下的宮位類型，可為空
    this.wholeSignHouse, // 整宮制下的宮位數字，可為空
    this.isPlanetDiurnal = true, // 默認為日間行星
    this.isSignMasculine = true, // 默認為陽性星座
    this.sectStatus = SectStatus.outOfSect, // 默認為不在其區分位置
  });

  /// 从 JSON 创建 PlanetPosition 对象
  factory PlanetPosition.fromJson(Map<String, dynamic> json) {
    return PlanetPosition(
      id: json['id'] as int,
      name: json['name'] as String,
      symbol: json['symbol'] as String,
      longitude: json['longitude'].toDouble(),
      latitude: json['latitude'].toDouble(),
      distance: json['distance'].toDouble(),
      longitudeSpeed: json['longitudeSpeed'].toDouble(),
      latitudeSpeed: json['latitudeSpeed'].toDouble(),
      distanceSpeed: json['distanceSpeed'].toDouble(),
      sign: json['sign'] as String,
      house: json['house'] as int,
      color: json['color'] != null ? Color(json['color'] as int) : Colors.grey,
      dignity: json['dignity'] != null
          ? PlanetDignity.values[json['dignity'] as int]
          : PlanetDignity.peregrine,
      solarCondition: json['solarCondition'] != null
          ? SolarCondition.values[json['solarCondition'] as int]
          : SolarCondition.free,
      isDaytime: json['isDaytime'] as bool? ?? true,
      houseType: json['houseType'] != null
          ? HouseType.values[json['houseType'] as int]
          : HouseType.angular,
      wholeSignHouseType: json['wholeSignHouseType'] != null
          ? HouseType.values[json['wholeSignHouseType'] as int]
          : null,
      wholeSignHouse: json['wholeSignHouse'] as int?,
      isPlanetDiurnal: json['isPlanetDiurnal'] as bool? ?? true,
      isSignMasculine: json['isSignMasculine'] as bool? ?? true,
      sectStatus: json['sectStatus'] != null
          ? SectStatus.values[json['sectStatus'] as int]
          : SectStatus.outOfSect,
    );
  }

  /// 将 PlanetPosition 对象转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'symbol': symbol,
      'longitude': longitude,
      'latitude': latitude,
      'distance': distance,
      'longitudeSpeed': longitudeSpeed,
      'latitudeSpeed': latitudeSpeed,
      'distanceSpeed': distanceSpeed,
      'sign': sign,
      'house': house,
      'color': color.value,
      'dignity': dignity.index,
      'solarCondition': solarCondition.index,
      'isDaytime': isDaytime,
      'houseType': houseType.index,
      'wholeSignHouseType': wholeSignHouseType?.index,
      'wholeSignHouse': wholeSignHouse,
      'isPlanetDiurnal': isPlanetDiurnal,
      'isSignMasculine': isSignMasculine,
      'sectStatus': sectStatus.index,
    };
  }

  /// 获取格式化的经度（度数）
  String getFormattedLongitude() {
    final int degrees = longitude.floor();
    final double minutes = (longitude - degrees) * 60;
    return '$degrees° ${minutes.toStringAsFixed(2)}\'';
  }

  /// 获取宫位文字
  String getHouseText() {
    return '落入第${house == 0 ? 12 : house}宮';
  }

  String getHouseTextDual(String primaryPerson) {
    return '落入$primaryPerson第${house == 0 ? 12 : house}宮';
  }

  /// 獲取行星尊貴力量狀態文字
  String getDignityText() {
    switch (dignity) {
      case PlanetDignity.domicile:
        return '廟';
      case PlanetDignity.exaltation:
        return '旺';
      case PlanetDignity.triplicity:
        return '三分';
      case PlanetDignity.terms:
        return '界';
      case PlanetDignity.decan:
        return '十';
      case PlanetDignity.detriment:
        return '陷';
      case PlanetDignity.fall:
        return '弱';
      case PlanetDignity.peregrine:
        return '外來的';
    }
  }

  /// 獲取行星與太陽關係狀態文字
  String getSolarConditionText() {
    switch (solarCondition) {
      case SolarCondition.cazimi:
        return '核心內';
      case SolarCondition.combust:
        return '焦傷';
      case SolarCondition.underBeams:
        return '在光束下';
      case SolarCondition.free:
        return '正常';
    }
  }

  /// 獲取日夜區分文字
  String getDayNightText() {
    return isDaytime ? '日間盤' : '夜間盤';
  }

  /// 獲取行星日夜屬性文字
  String getPlanetSectNatureText() {
    return isPlanetDiurnal ? '日間行星' : '夜間行星';
  }

  /// 獲取星座陰陽性文字
  String getSignPolarityText() {
    return isSignMasculine ? '陽性星座' : '陰性星座';
  }

  /// 獲取日夜區分狀態文字
  String getSectStatusText() {
    switch (sectStatus) {
      case SectStatus.inSect:
        return '在其區分位置'; // 行星與盤的日夜屬性一致，且在正確的半球上
      case SectStatus.outOfSect:
        return '不在其區分位置';
      case SectStatus.hayz:
        return '場域（在其區分位置且符合星座陰陽）'; // 場域：行星處於正確的日夜性質、半球、星座陰陽性質上，是最完美的區分狀態
    }
  }

  /// 獲取宮位類型文字
  String getHouseTypeText() {
    switch (houseType) {
      case HouseType.angular:
        return '始宮';
      case HouseType.succedent:
        return '續宮';
      case HouseType.cadent:
        return '果宮';
    }
  }

  /// 獲取整宮制下的宮位類型文字
  String getWholeSignHouseTypeText() {
    if (wholeSignHouseType == null) return '未知';

    switch (wholeSignHouseType!) {
      case HouseType.angular:
        return '始宮';
      case HouseType.succedent:
        return '續宮';
      case HouseType.cadent:
        return '果宮';
    }
  }

  /// 獲取整宮制下的宮位數字文字
  String getWholeSignHouseText() {
    if (wholeSignHouse == null) return '未知';
    return '第 $wholeSignHouse 宮';
  }

  PlanetPosition copyWith({
    double? longitude,
    double? latitude,
    String? sign,
    int? house,
    List<AspectInfo>? aspects,
    List<AspectInfo>? natalAspects,
    List<AspectInfo>? transitAspects,
  }) {
    return PlanetPosition(
      id: id,
      name: name,
      symbol: symbol,
      color: color,
      longitude: longitude ?? this.longitude,
      latitude: latitude ?? this.latitude,
      distance: distance,
      longitudeSpeed: longitudeSpeed,
      latitudeSpeed: latitudeSpeed,
      distanceSpeed: distanceSpeed,
      sign: sign ?? this.sign,
      house: house ?? this.house,
      dignity: dignity,
      solarCondition: solarCondition,
      isDaytime: isDaytime,
      houseType: houseType,
      wholeSignHouseType: wholeSignHouseType,
      wholeSignHouse: wholeSignHouse,
      isPlanetDiurnal: isPlanetDiurnal,
      isSignMasculine: isSignMasculine,
      sectStatus: sectStatus,
    );
  }

  /// 獲取所有相位（包括一般相位、本命相位和行運相位）
  List<AspectInfo> getAllAspects() {
    final allAspects = <AspectInfo>[];
    return allAspects;
  }

  /// 獲取與特定行星的相位
  List<AspectInfo> getAspectsWithPlanet(String planetName) {
    return getAllAspects().where((aspect) =>
        aspect.planet1.name == planetName || aspect.planet2.name == planetName).toList();
  }

  /// 獲取特定類型的相位（如合相、對分相等）
  List<AspectInfo> getAspectsByType(String aspectType) {
    return getAllAspects().where((aspect) => aspect.aspect == aspectType).toList();
  }

  /// 獲取入相的相位
  List<AspectInfo> getApplyingAspects() {
    return getAllAspects().where((aspect) =>
        aspect.direction == AspectDirection.applying).toList();
  }

  /// 獲取出相的相位
  List<AspectInfo> getSeparatingAspects() {
    return getAllAspects().where((aspect) =>
        aspect.direction == AspectDirection.separating).toList();
  }

  /// 獲取互容接納關係
  List<AspectInfo> getReceptions() {
    return getAllAspects().where((aspect) =>
        aspect.receptionType != ReceptionType.none).toList();
  }

  /// 檢查是否與特定行星有相位
  bool hasAspectWith(String planetName) {
    return getAspectsWithPlanet(planetName).isNotEmpty;
  }

  /// 檢查是否有特定類型的相位
  bool hasAspectType(String aspectType) {
    return getAspectsByType(aspectType).isNotEmpty;
  }

  /// 獲取最緊密的相位（容許度最小的相位）
  AspectInfo? getTightestAspect() {
    final allAspects = getAllAspects();
    if (allAspects.isEmpty) return null;

    return allAspects.reduce((a, b) => a.orb < b.orb ? a : b);
  }

  /// 獲取相位數量統計
  Map<String, int> getAspectStats() {
    final stats = <String, int>{};
    final allAspects = getAllAspects();

    for (final aspect in allAspects) {
      stats[aspect.aspect] = (stats[aspect.aspect] ?? 0) + 1;
    }

    return stats;
  }
}
