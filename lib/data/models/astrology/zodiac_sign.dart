/// 星座模型
class ZodiacSign {
  /// 星座 ID (1-12)
  final int id;
  
  /// 星座名稱
  final String name;
  
  /// 星座符號
  final String symbol;
  
  /// 星座元素（火、土、風、水）
  final String element;
  
  /// 星座性質（基本、固定、變動）
  final String quality;
  
  /// 星座陰陽性（true=陽性，false=陰性）
  final bool isPositive;
  
  /// 主宰行星
  final String rulingPlanet;
  
  /// 現代主宰行星（如果有）
  final String? modernRulingPlanet;

  const ZodiacSign({
    required this.id,
    required this.name,
    required this.symbol,
    required this.element,
    required this.quality,
    required this.isPositive,
    required this.rulingPlanet,
    this.modernRulingPlanet,
  });

  /// 從星座 ID 創建星座對象
  factory ZodiacSign.fromId(int id) {
    switch (id) {
      case 1:
        return const ZodiacSign(
          id: 1,
          name: '牡羊座',
          symbol: '♈',
          element: '火',
          quality: '基本',
          isPositive: true,
          rulingPlanet: '火星',
        );
      case 2:
        return const ZodiacSign(
          id: 2,
          name: '金牛座',
          symbol: '♉',
          element: '土',
          quality: '固定',
          isPositive: false,
          rulingPlanet: '金星',
        );
      case 3:
        return const ZodiacSign(
          id: 3,
          name: '雙子座',
          symbol: '♊',
          element: '風',
          quality: '變動',
          isPositive: true,
          rulingPlanet: '水星',
        );
      case 4:
        return const ZodiacSign(
          id: 4,
          name: '巨蟹座',
          symbol: '♋',
          element: '水',
          quality: '基本',
          isPositive: false,
          rulingPlanet: '月亮',
        );
      case 5:
        return const ZodiacSign(
          id: 5,
          name: '獅子座',
          symbol: '♌',
          element: '火',
          quality: '固定',
          isPositive: true,
          rulingPlanet: '太陽',
        );
      case 6:
        return const ZodiacSign(
          id: 6,
          name: '處女座',
          symbol: '♍',
          element: '土',
          quality: '變動',
          isPositive: false,
          rulingPlanet: '水星',
        );
      case 7:
        return const ZodiacSign(
          id: 7,
          name: '天秤座',
          symbol: '♎',
          element: '風',
          quality: '基本',
          isPositive: true,
          rulingPlanet: '金星',
        );
      case 8:
        return const ZodiacSign(
          id: 8,
          name: '天蠍座',
          symbol: '♏',
          element: '水',
          quality: '固定',
          isPositive: false,
          rulingPlanet: '火星',
          modernRulingPlanet: '冥王星',
        );
      case 9:
        return const ZodiacSign(
          id: 9,
          name: '射手座',
          symbol: '♐',
          element: '火',
          quality: '變動',
          isPositive: true,
          rulingPlanet: '木星',
        );
      case 10:
        return const ZodiacSign(
          id: 10,
          name: '摩羯座',
          symbol: '♑',
          element: '土',
          quality: '基本',
          isPositive: false,
          rulingPlanet: '土星',
        );
      case 11:
        return const ZodiacSign(
          id: 11,
          name: '水瓶座',
          symbol: '♒',
          element: '風',
          quality: '固定',
          isPositive: true,
          rulingPlanet: '土星',
          modernRulingPlanet: '天王星',
        );
      case 12:
        return const ZodiacSign(
          id: 12,
          name: '雙魚座',
          symbol: '♓',
          element: '水',
          quality: '變動',
          isPositive: false,
          rulingPlanet: '木星',
          modernRulingPlanet: '海王星',
        );
      default:
        return const ZodiacSign(
          id: 1,
          name: '牡羊座',
          symbol: '♈',
          element: '火',
          quality: '基本',
          isPositive: true,
          rulingPlanet: '火星',
        );
    }
  }

  /// 從黃經度數創建星座對象
  factory ZodiacSign.fromLongitude(double longitude) {
    // 將度數標準化到 0-360 範圍
    final normalizedLongitude = longitude % 360;
    
    // 每個星座佔 30 度
    final zodiacId = (normalizedLongitude / 30).floor() + 1;
    
    return ZodiacSign.fromId(zodiacId);
  }

  /// 從星座名稱創建星座對象
  factory ZodiacSign.fromName(String name) {
    const nameToId = {
      '牡羊座': 1,
      '金牛座': 2,
      '雙子座': 3,
      '巨蟹座': 4,
      '獅子座': 5,
      '處女座': 6,
      '天秤座': 7,
      '天蠍座': 8,
      '射手座': 9,
      '摩羯座': 10,
      '水瓶座': 11,
      '雙魚座': 12,
    };
    
    final id = nameToId[name] ?? 1;
    return ZodiacSign.fromId(id);
  }

  /// 獲取所有星座
  static List<ZodiacSign> get allSigns {
    return List.generate(12, (index) => ZodiacSign.fromId(index + 1));
  }

  /// 獲取星座在黃道上的起始度數
  double get startDegree => (id - 1) * 30.0;

  /// 獲取星座在黃道上的結束度數
  double get endDegree => id * 30.0;

  /// 檢查給定度數是否在此星座範圍內
  bool containsDegree(double longitude) {
    final normalizedLongitude = longitude % 360;
    return normalizedLongitude >= startDegree && normalizedLongitude < endDegree;
  }

  /// 獲取星座的對宮星座
  ZodiacSign get oppositeSign {
    final oppositeId = ((id + 5) % 12) + 1;
    return ZodiacSign.fromId(oppositeId);
  }

  /// 獲取同元素的星座
  List<ZodiacSign> get sameElementSigns {
    return allSigns.where((sign) => sign.element == element).toList();
  }

  /// 獲取同性質的星座
  List<ZodiacSign> get sameQualitySigns {
    return allSigns.where((sign) => sign.quality == quality).toList();
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'symbol': symbol,
      'element': element,
      'quality': quality,
      'isPositive': isPositive,
      'rulingPlanet': rulingPlanet,
      'modernRulingPlanet': modernRulingPlanet,
    };
  }

  /// 從 JSON 創建星座對象
  factory ZodiacSign.fromJson(Map<String, dynamic> json) {
    return ZodiacSign(
      id: json['id'] as int,
      name: json['name'] as String,
      symbol: json['symbol'] as String,
      element: json['element'] as String,
      quality: json['quality'] as String,
      isPositive: json['isPositive'] as bool,
      rulingPlanet: json['rulingPlanet'] as String,
      modernRulingPlanet: json['modernRulingPlanet'] as String?,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ZodiacSign && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => '$name ($symbol)';
}
