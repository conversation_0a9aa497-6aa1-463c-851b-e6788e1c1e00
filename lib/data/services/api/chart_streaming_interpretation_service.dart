import 'package:shared_preferences/shared_preferences.dart';

import '../../../astreal.dart';
import 'ai_streaming_service.dart';
import 'astrology_service.dart';
import 'chart_interpretation_service.dart';

/// 星盤流式解讀服務
/// 提供即時顯示的星盤解讀功能
class ChartStreamingInterpretationService {
  /// 獲取流式星盤解讀
  static Stream<AIStreamingResponse> getStreamingChartInterpretation(
    ChartData chartData,
    String interpretationTitle,
    String subtitle, {
    String? keyPoint,
  }) async* {
    try {
      logger.i("開始流式星盤解讀");

      // 先發送一個狀態響應，確保 Stream 開始工作
      yield AIStreamingResponse(
        type: AIStreamingResponseType.status,
        content: '正在準備星盤解讀...',
        isComplete: false,
      );

      // 確認解讀模式和分析方式
      yield AIStreamingResponse(
        type: AIStreamingResponseType.status,
        content: '正在確認解讀設置...',
        isComplete: false,
      );

      final prefs = await SharedPreferences.getInstance();
      final userMode = prefs.getString('user_mode') ?? 'starmaster';
      final analysisMethod = prefs.getString('analysis_method') ?? 'modern';

      logger.i("解讀模式：$userMode");
      logger.i("分析方式：$analysisMethod");

      // 根據分析方式調整星盤設置
      yield AIStreamingResponse(
        type: AIStreamingResponseType.status,
        content: '正在調整星盤配置...',
        isComplete: false,
      );

      ChartSettings chartSettings = await ChartSettings.loadFromPrefs();
      chartSettings.planetVisibility = planetVisibilityAnalysis;
      chartSettings.aspectOrbs = aspectOrbsAnalysis;

      // 根據分析方式設定宮位制
      if (analysisMethod == 'classical') {
        // 古典占星使用整宮制
        chartSettings.houseSystem = HouseSystem.wholeSign;
        logger.i("設定為古典占星：使用整宮制");
      } else {
        // 現代占星使用普拉西德制
        chartSettings.houseSystem = HouseSystem.placidus;
        logger.i("設定為現代占星：使用普拉西德制");
      }

      // 保存設置
      // await chartSettings.saveToPrefs();

      // 重新計算星盤數據以應用新設置
      yield AIStreamingResponse(
        type: AIStreamingResponseType.status,
        content: '正在重新計算星盤數據...',
        isComplete: false,
      );

      chartData = await AstrologyService()
          .calculateChartData(chartData, chartSettings: chartSettings);

      // 構建提示詞（包含模式和分析方式信息）
      String prompt;
      try {
        prompt = await ChartInterpretationService.getPrompt(
          chartData,
          interpretationTitle,
          subtitle,
          keyPoint: keyPoint,
          userMode: userMode,
          analysisMethod: analysisMethod,
        );
      } catch (e) {
        logger.e("提示詞構建失敗：$e");
        yield AIStreamingResponse(
          type: AIStreamingResponseType.error,
          content: '提示詞構建失敗：$e',
          isComplete: true,
        );
        return;
      }

      // 發送另一個狀態響應
      yield AIStreamingResponse(
        type: AIStreamingResponseType.status,
        content: '正在開始分析...',
        isComplete: false,
      );
      // 調用流式 AI 服務
      await for (final response in AIStreamingService.getStreamingChartInterpretation(
        chartData: chartData,
        prompt: prompt,
      )) {
        yield response;
      }

      logger.i("流式星盤解讀完成");
    } catch (e) {
      logger.e("流式星盤解讀失敗：$e");
      yield AIStreamingResponse(
        type: AIStreamingResponseType.error,
        content: '解讀服務暫時不可用，請稍後再試。',
        isComplete: true,
      );
    }
  }

  /// 獲取流式自定義解讀
  static Stream<AIStreamingResponse> getStreamingCustomInterpretation({
    required ChartData chartData,
    required String customPrompt,
  }) async* {
    try {
      logger.i("開始流式自定義星盤解讀");
      logger.d("自定義提示詞：$customPrompt");

      // 直接調用流式 AI 服務
      await for (final response in AIStreamingService.getStreamingChartInterpretation(
        chartData: chartData,
        prompt: customPrompt,
      )) {
        yield response;
      }

      logger.i("流式自定義星盤解讀完成");
    } catch (e) {
      logger.e("流式自定義星盤解讀失敗：$e");
      yield AIStreamingResponse(
        type: AIStreamingResponseType.error,
        content: '解讀服務暫時不可用，請稍後再試。',
        isComplete: true,
      );
    }
  }
}
