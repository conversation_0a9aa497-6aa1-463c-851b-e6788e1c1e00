import 'dart:async';
import 'dart:convert';

import 'package:http/http.dart' as http;

import '../../../core/utils/logger_utils.dart';
import '../../models/astrology/chart_data.dart';
import 'ai_api_service.dart';
import 'ai_usage_stats_service.dart';
import 'firebase_ai_usage_service.dart';

/// AI 流式響應服務
/// 支援 OpenAI、Anthropic、Groq 等提供商的流式 API
class AIStreamingService {
  /// 獲取模型配置
  static Future<AIModelConfig?> _getModelConfig(String modelId) async {
    try {
      final availableModels = AIApiService.availableModels;
      for (final model in availableModels) {
        if (model.id == modelId) {
          return model;
        }
      }
      logger.w('找不到模型配置: $modelId');
      return null;
    } catch (e) {
      logger.e('獲取模型配置失敗: $e');
      return null;
    }
  }
  /// 獲取流式星盤解讀
  static Stream<AIStreamingResponse> getStreamingChartInterpretation({
    required ChartData chartData,
    required String prompt,
    String? customModelId,
  }) async* {
    try {
      logger.i("====== AI 流式星盤解讀請求開始 ======");

      // 立即發送一個狀態響應，確保 Stream 開始工作
      yield AIStreamingResponse(
        type: AIStreamingResponseType.status,
        content: '正在初始化 AI 服務...',
        isComplete: false,
      );

      final startTime = DateTime.now();
      final modelId = customModelId ?? await AIApiService.getSelectedModel();

      // 獲取模型配置
      final modelConfig = await _getModelConfig(modelId);

      if (modelConfig == null) {
        logger.w('不支援的 AI 模型: $modelId');
        yield AIStreamingResponse(
          type: AIStreamingResponseType.error,
          content: '設定有誤，請聯繫管理員。',
          isComplete: true,
        );
        return;
      }

      logger.i("選擇的模型：${modelConfig.name} ($modelId)");
      logger.i("API 提供商：${modelConfig.provider.name}");

      // 檢查 API Key
      final isConfigured = await AIApiService.isApiKeyConfigured(modelConfig.provider);
      if (!isConfigured) {
        logger.w('${modelConfig.provider.name} API Key 未設置');
        yield AIStreamingResponse(
          type: AIStreamingResponseType.error,
          content: '設定有誤，請聯繫管理員。',
          isComplete: true,
        );
        return;
      }

      // 重新計算星盤數據（如果需要）
      if (chartData.planets == null || chartData.houses == null) {
        logger.w('數據為空，重新計算');
        yield AIStreamingResponse(
          type: AIStreamingResponseType.status,
          content: '正在計算星盤數據...',
          isComplete: false,
        );
        
        // 這裡需要重新計算，但為了簡化，我們先跳過
        // chartData = await AstrologyService().calculateChartData(chartData, chartSettings: chartSettings);
      }

      // 構建星盤數據摘要
      yield AIStreamingResponse(
        type: AIStreamingResponseType.status,
        content: '正在準備星盤資料...',
        isComplete: false,
      );

      final chartSummary = await AIApiService.buildChartSummary(chartData);
      final fullPrompt = await AIApiService.buildFullPrompt(prompt, chartSummary);

      logger.d("完整提示詞：\n$fullPrompt");

      // 檢查使用量限制
      final estimatedTokens = AIUsageStatsService.estimateTokens(fullPrompt) + 1000;
      final canUse = await FirebaseAIUsageService.canUseProvider(
        provider: modelConfig.provider,
        estimatedTokens: estimatedTokens,
      );

      if (!canUse) {
        logger.w('⚠️ 今日 ${modelConfig.provider.displayName} 使用量已達上限');
        yield AIStreamingResponse(
          type: AIStreamingResponseType.error,
          content: '今日使用量已達上限，請明日再試或者聯繫管理員。',
          isComplete: true,
        );
        return;
      }

      logger.i("✅ 使用量檢查通過，預估使用 $estimatedTokens tokens");

      yield AIStreamingResponse(
        type: AIStreamingResponseType.status,
        content: '正在連接服務...',
        isComplete: false,
      );

      // 根據提供商調用相應的流式 API
      await for (final response in _callStreamingAPI(modelConfig, fullPrompt)) {
        yield response;
      }

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      logger.i("流式請求完成，耗時：${duration.inMilliseconds}ms");

    } catch (e) {
      logger.e("AI 流式解讀請求失敗：$e");
      yield AIStreamingResponse(
        type: AIStreamingResponseType.error,
        content: '解讀服務暫時不可用，請稍後再試。',
        isComplete: true,
      );
    }
  }

  /// 調用流式 API
  static Stream<AIStreamingResponse> _callStreamingAPI(
    AIModelConfig config,
    String prompt,
  ) async* {
    switch (config.provider) {
      case AIProvider.openai:
        await for (final response in _callOpenAIStreamingAPI(config, prompt)) {
          yield response;
        }
        break;
      case AIProvider.anthropic:
        await for (final response in _callAnthropicStreamingAPI(config, prompt)) {
          yield response;
        }
        break;
      case AIProvider.groq:
        await for (final response in _callGroqStreamingAPI(config, prompt)) {
          yield response;
        }
        break;
      case AIProvider.gemini:
        await for (final response in _callGeminiStreamingAPI(config, prompt)) {
          yield response;
        }
        break;
    }
  }

  /// 調用 OpenAI 流式 API
  static Stream<AIStreamingResponse> _callOpenAIStreamingAPI(
    AIModelConfig config,
    String prompt,
  ) async* {
    final apiKey = await AIApiService.getOpenAIApiKey();
    if (apiKey == null || apiKey.isEmpty) {
      yield AIStreamingResponse(
        type: AIStreamingResponseType.error,
        content: 'OpenAI API Key 未設置',
        isComplete: true,
      );
      return;
    }

    final requestBody = {
      'model': config.id,
      'messages': [
        {
          'role': 'system',
          'content': '你是一位專業的占星師，擅長解讀星盤並提供深入的分析。請用繁體中文回答。',
        },
        {
          'role': 'user',
          'content': prompt,
        },
      ],
      'max_tokens': config.maxTokens,
      'temperature': config.temperature,
      'stream': true, // 啟用流式響應
    };

    logger.d("OpenAI 流式請求內容：\n${jsonEncode(requestBody)}");

    final request = http.Request('POST', Uri.parse(config.endpoint));
    request.headers.addAll({
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $apiKey',
    });
    request.body = jsonEncode(requestBody);

    final streamedResponse = await request.send();

    if (streamedResponse.statusCode != 200) {
      final errorBody = await streamedResponse.stream.bytesToString();
      logger.e("OpenAI 流式 API 請求失敗：${streamedResponse.statusCode} - $errorBody");
      yield AIStreamingResponse(
        type: AIStreamingResponseType.error,
        content: 'OpenAI API 請求失敗',
        isComplete: true,
      );
      return;
    }

    yield AIStreamingResponse(
      type: AIStreamingResponseType.status,
      content: '開始接收回應...',
      isComplete: false,
    );

    final StringBuffer contentBuffer = StringBuffer();
    int totalTokens = 0;
    int promptTokens = 0;
    int completionTokens = 0;

    await for (final chunk in streamedResponse.stream.transform(utf8.decoder)) {
      final lines = chunk.split('\n');

      for (final line in lines) {
        if (line.trim().isEmpty) continue;
        if (!line.startsWith('data: ')) continue;
        
        final data = line.substring(6).trim();
        if (data == '[DONE]') {
          // 流式響應結束
          yield AIStreamingResponse(
            type: AIStreamingResponseType.complete,
            content: contentBuffer.toString(),
            isComplete: true,
            totalTokens: totalTokens > 0 ? totalTokens : null,
            promptTokens: promptTokens > 0 ? promptTokens : null,
            completionTokens: completionTokens > 0 ? completionTokens : null,
          );
          
          // 記錄使用統計
          await _recordStreamingUsageStats(
            config.provider,
            prompt,
            contentBuffer.toString(),
            totalTokens,
            promptTokens,
            completionTokens,
          );
          return;
        }

        try {
          final jsonData = jsonDecode(data);
          
          // 提取 token 使用量（如果有）
          if (jsonData['usage'] != null) {
            totalTokens = jsonData['usage']['total_tokens'] ?? 0;
            promptTokens = jsonData['usage']['prompt_tokens'] ?? 0;
            completionTokens = jsonData['usage']['completion_tokens'] ?? 0;
          }
          
          if (jsonData['choices'] != null && jsonData['choices'].isNotEmpty) {
            final choice = jsonData['choices'][0];
            final delta = choice['delta'];
            
            if (delta != null && delta['content'] != null) {
              final content = delta['content'] as String;
              contentBuffer.write(content);
              
              yield AIStreamingResponse(
                type: AIStreamingResponseType.content,
                content: content,
                fullContent: contentBuffer.toString(),
                isComplete: false,
              );
            }
          }
        } catch (e) {
          logger.w("解析 OpenAI 流式響應時出錯：$e, 數據：$data");
        }
      }
    }
  }

  /// 調用 Anthropic 流式 API
  static Stream<AIStreamingResponse> _callAnthropicStreamingAPI(
    AIModelConfig config,
    String prompt,
  ) async* {
    // Anthropic 的流式 API 實現
    // 由於篇幅限制，這裡先提供基本結構
    yield AIStreamingResponse(
      type: AIStreamingResponseType.error,
      content: 'Anthropic 流式 API 尚未實現',
      isComplete: true,
    );
  }

  /// 調用 Groq 流式 API
  static Stream<AIStreamingResponse> _callGroqStreamingAPI(
    AIModelConfig config,
    String prompt,
  ) async* {
    // Groq 使用 OpenAI 相容的 API，可以複用 OpenAI 的實現
    await for (final response in _callOpenAIStreamingAPI(config, prompt)) {
      yield response;
    }
  }

  /// 調用 Gemini 流式 API
  static Stream<AIStreamingResponse> _callGeminiStreamingAPI(
    AIModelConfig config,
    String prompt,
  ) async* {
    // Gemini 的流式 API 實現
    // 由於篇幅限制，這裡先提供基本結構
    yield AIStreamingResponse(
      type: AIStreamingResponseType.error,
      content: 'Gemini 流式 API 尚未實現',
      isComplete: true,
    );
  }

  /// 記錄流式使用統計
  static Future<void> _recordStreamingUsageStats(
    AIProvider provider,
    String prompt,
    String response,
    int totalTokens,
    int promptTokens,
    int completionTokens,
  ) async {
    try {
      // 使用實際的 token 數量，如果沒有則估算
      final actualTokens = totalTokens > 0 
          ? totalTokens 
          : AIUsageStatsService.estimateRequestTokens(
              prompt: prompt,
              response: response,
            );

      logger.i("✅ 流式響應完成，使用 token 數量：$actualTokens");

      // 記錄到 Firebase
      await FirebaseAIUsageService.recordUsage(
        provider: provider,
        actualTokens: actualTokens,
        prompt: prompt,
        response: response,
      );

      // 記錄到本地
      await AIUsageStatsService.recordUsage(
        provider: provider,
        tokens: actualTokens,
      );

      logger.d("記錄流式使用統計：${provider.displayName} - $actualTokens tokens");
    } catch (e) {
      logger.w("記錄流式使用統計失敗：$e");
    }
  }
}

/// AI 流式響應類型
enum AIStreamingResponseType {
  status,    // 狀態更新
  content,   // 內容片段
  complete,  // 完成
  error,     // 錯誤
}

/// AI 流式響應數據模型
class AIStreamingResponse {
  final AIStreamingResponseType type;
  final String content;
  final String? fullContent;
  final bool isComplete;
  final int? totalTokens;
  final int? promptTokens;
  final int? completionTokens;

  const AIStreamingResponse({
    required this.type,
    required this.content,
    this.fullContent,
    required this.isComplete,
    this.totalTokens,
    this.promptTokens,
    this.completionTokens,
  });

  @override
  String toString() {
    return 'AIStreamingResponse(type: $type, content: ${content.length} chars, isComplete: $isComplete)';
  }
}
