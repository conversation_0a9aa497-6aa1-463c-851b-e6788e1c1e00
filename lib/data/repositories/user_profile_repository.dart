import 'dart:convert';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart' as foundation;
import 'package:http/http.dart' as http;

import '../../core/config/firebase_config_windows.dart';
import '../../core/constants/firebase_collections.dart';
import '../../core/utils/logger_utils.dart';
import '../models/user/user_profile.dart';

/// 用戶檔案資料庫存取層
/// 統一管理所有與 user_profiles 集合相關的操作
class UserProfileRepository {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = FirebaseCollections.userProfiles;

  // ==================== 基本 CRUD 操作 ====================

  /// 根據 UID 獲取用戶檔案
  static Future<UserProfile?> getUserById(String userId) async {
    try {
      logger.i('獲取用戶檔案: $userId');

      Map<String, dynamic>? data;
      if (_shouldUseRestApi()) {
        data = await _getUserViaRestApi(userId);
      } else {
        data = await _getUserViaSDK(userId);
      }

      if (data != null) {
        logger.i('成功獲取用戶檔案: $userId');
        return UserProfile.fromJson(data);
      } else {
        logger.w('用戶檔案不存在: $userId');
        return null;
      }
    } catch (e) {
      logger.e('獲取用戶檔案失敗: $e');
      rethrow;
    }
  }

  /// 更新用戶檔案
  static Future<void> updateUser(String userId, UserProfile userProfile) async {
    try {
      logger.i('更新用戶檔案: $userId');

      final data = userProfile.toFirestoreJson(isUpdate: true);
      
      if (_shouldUseRestApi()) {
        await _updateUserViaRestApi(userId, data);
      } else {
        await _updateUserViaSDK(userId, data);
      }

      logger.i('用戶檔案更新成功: $userId');
    } catch (e) {
      logger.e('更新用戶檔案失敗: $e');
      rethrow;
    }
  }

  /// 更新用戶登入資料（專門用於登入時間和次數）
  static Future<void> updateUserLoginData(String userId, Map<String, dynamic> loginData) async {
    try {
      logger.i('更新用戶登入資料: $userId');

      if (_shouldUseRestApi()) {
        await _updateUserViaRestApi(userId, loginData);
      } else {
        await _updateUserViaSDK(userId, loginData);
      }

      logger.i('用戶登入資料更新成功: $userId');
    } catch (e) {
      logger.e('更新用戶登入資料失敗: $e');
      rethrow;
    }
  }

  /// 刪除用戶檔案
  static Future<void> deleteUser(String userId) async {
    try {
      logger.i('刪除用戶檔案: $userId');
      
      if (_shouldUseRestApi()) {
        await _deleteUserViaRestApi(userId);
      } else {
        await _deleteUserViaSDK(userId);
      }
      
      logger.i('用戶檔案刪除成功: $userId');
    } catch (e) {
      logger.e('刪除用戶檔案失敗: $e');
      rethrow;
    }
  }

  // ==================== 批量操作 ====================

  /// 獲取所有用戶
  static Future<List<UserProfile>> getAllUsers({
    int? limit,
    String? orderBy = 'created_at',
    bool descending = true,
  }) async {
    try {
      logger.i('獲取用戶列表...');

      Query query = _firestore.collection(_collection);

      if (orderBy != null) {
        query = query.orderBy(orderBy, descending: descending);
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      final querySnapshot = await query.get();

      final users = querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return UserProfile.fromJson(data);
      }).toList();

      logger.i('成功獲取 ${users.length} 個用戶');
      return users;
    } catch (e) {
      logger.e('獲取用戶列表失敗: $e');
      rethrow;
    }
  }

  /// 批量更新用戶
  static Future<void> batchUpdateUsers(
    List<String> userIds,
    Map<String, dynamic> updateData,
  ) async {
    try {
      logger.i('批量更新 ${userIds.length} 個用戶');
      
      final batch = _firestore.batch();
      updateData['updated_at'] = FieldValue.serverTimestamp();
      
      for (final userId in userIds) {
        final docRef = _firestore.collection(_collection).doc(userId);
        batch.update(docRef, updateData);
      }
      
      await batch.commit();
      
      logger.i('批量更新完成');
    } catch (e) {
      logger.e('批量更新失敗: $e');
      rethrow;
    }
  }

  // ==================== 查詢操作 ====================

  /// 搜尋用戶
  static Future<List<UserProfile>> searchUsers({
    String? email,
    String? displayName,
    bool? emailVerified,
    bool? isAnonymous,
    bool? isAdmin,
    int? limit = 50,
  }) async {
    try {
      logger.i('搜尋用戶...');
      
      Query query = _firestore.collection(_collection);
      
      // 添加篩選條件
      if (email != null && email.isNotEmpty) {
        query = query.where('email', isEqualTo: email);
      }
      
      if (displayName != null && displayName.isNotEmpty) {
        query = query.where('display_name', isEqualTo: displayName);
      }
      
      if (emailVerified != null) {
        query = query.where('email_verified', isEqualTo: emailVerified);
      }
      
      if (isAnonymous != null) {
        query = query.where('is_anonymous', isEqualTo: isAnonymous);
      }
      
      if (isAdmin != null) {
        query = query.where('is_admin', isEqualTo: isAdmin);
      }
      
      if (limit != null) {
        query = query.limit(limit);
      }
      
      final querySnapshot = await query.get();
      
      final users = querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return UserProfile.fromJson(data);
      }).toList();
      
      logger.i('搜尋到 ${users.length} 個用戶');
      return users;
    } catch (e) {
      logger.e('搜尋用戶失敗: $e');
      rethrow;
    }
  }

  /// 獲取管理員用戶列表
  static Future<List<String>> getAdminUserIds() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('is_admin', isEqualTo: true)
          .get();
      
      final adminIds = querySnapshot.docs.map((doc) => doc.id).toList();
      
      logger.i('找到 ${adminIds.length} 個管理員');
      return adminIds;
    } catch (e) {
      logger.e('獲取管理員列表失敗: $e');
      rethrow;
    }
  }

  // ==================== 統計操作 ====================

  /// 獲取用戶統計資料
  static Future<Map<String, int>> getUserStats() async {
    try {
      logger.i('獲取用戶統計資料...');
      
      // 總用戶數
      final totalUsersSnapshot = await _firestore.collection(_collection).get();
      final totalUsers = totalUsersSnapshot.docs.length;
      
      // 已驗證用戶數
      final verifiedUsersSnapshot = await _firestore
          .collection(_collection)
          .where('email_verified', isEqualTo: true)
          .get();
      final verifiedUsers = verifiedUsersSnapshot.docs.length;
      
      // 匿名用戶數
      final anonymousUsersSnapshot = await _firestore
          .collection(_collection)
          .where('is_anonymous', isEqualTo: true)
          .get();
      final anonymousUsers = anonymousUsersSnapshot.docs.length;
      
      // 管理員用戶數
      final adminUsersSnapshot = await _firestore
          .collection(_collection)
          .where('is_admin', isEqualTo: true)
          .get();
      final adminUsers = adminUsersSnapshot.docs.length;
      
      // 今日新用戶數
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final todayUsersSnapshot = await _firestore
          .collection(_collection)
          .where('created_at', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .get();
      final todayUsers = todayUsersSnapshot.docs.length;
      
      final stats = {
        'totalUsers': totalUsers,
        'verifiedUsers': verifiedUsers,
        'anonymousUsers': anonymousUsers,
        'adminUsers': adminUsers,
        'todayUsers': todayUsers,
      };
      
      logger.i('用戶統計資料: $stats');
      return stats;
    } catch (e) {
      logger.e('獲取用戶統計資料失敗: $e');
      return {
        'totalUsers': 0,
        'verifiedUsers': 0,
        'anonymousUsers': 0,
        'adminUsers': 0,
        'todayUsers': 0,
      };
    }
  }

  // ==================== 即時監聽 ====================

  /// 監聽用戶資料變更
  static Stream<List<UserProfile>> watchUsers({
    int? limit,
    String? orderBy = 'created_at',
    bool descending = true,
  }) {
    try {
      logger.i('開始監聽用戶資料變更');

      Query query = _firestore.collection(_collection);

      if (orderBy != null) {
        query = query.orderBy(orderBy, descending: descending);
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      return query.snapshots().map((snapshot) {
        return snapshot.docs.map((doc) {
          final data = doc.data() as Map<String, dynamic>;
          return UserProfile.fromJson(data);
        }).toList();
      });
    } catch (e) {
      logger.e('監聽用戶資料失敗: $e');
      rethrow;
    }
  }

  /// 監聽單個用戶資料變更
  static Stream<UserProfile?> watchUser(String userId) {
    try {
      logger.i('開始監聽用戶資料變更: $userId');

      return _firestore
          .collection(_collection)
          .doc(userId)
          .snapshots()
          .map((snapshot) {
        if (snapshot.exists) {
          final data = snapshot.data()!;
          return UserProfile.fromJson(data);
        }
        return null;
      });
    } catch (e) {
      logger.e('監聽用戶資料失敗: $e');
      rethrow;
    }
  }

  // ==================== 私有輔助方法 ====================

  /// 判斷是否使用 REST API
  static bool _shouldUseRestApi() {
    return Platform.isWindows && !foundation.kDebugMode;
  }

  // ==================== REST API 實作 ====================

  /// 通過 REST API 獲取用戶
  static Future<Map<String, dynamic>?> _getUserViaRestApi(String userId) async {
    try {
      final url = Uri.parse(
        '${FirebaseConfigWindows.firestoreBaseUrl}/projects/${FirebaseConfigWindows.projectId}/databases/(default)/documents/$_collection/$userId'
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['fields'] != null) {
          return _convertFirestoreFields(data['fields']);
        }
      } else if (response.statusCode == 404) {
        return null; // 文檔不存在
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }

      return null;
    } catch (e) {
      logger.e('REST API 獲取用戶失敗: $e');
      rethrow;
    }
  }

  /// 通過 REST API 創建用戶
  static Future<void> _createUserViaRestApi(String userId, Map<String, dynamic> data) async {
    try {
      final url = Uri.parse(
        '${FirebaseConfigWindows.firestoreBaseUrl}/projects/${FirebaseConfigWindows.projectId}/databases/(default)/documents/$_collection/$userId'
      );

      final firestoreData = _convertToFirestoreFields(data);

      final response = await http.patch(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'fields': firestoreData}),
      );

      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      logger.e('REST API 創建用戶失敗: $e');
      rethrow;
    }
  }

  /// 通過 REST API 更新用戶
  static Future<void> _updateUserViaRestApi(String userId, Map<String, dynamic> data) async {
    try {
      final url = Uri.parse(
        '${FirebaseConfigWindows.firestoreBaseUrl}/projects/${FirebaseConfigWindows.projectId}/databases/(default)/documents/$_collection/$userId'
      );

      final firestoreData = _convertToFirestoreFields(data);

      final response = await http.patch(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'fields': firestoreData,
          'updateMask': {'fieldPaths': data.keys.toList()},
        }),
      );

      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      logger.e('REST API 更新用戶失敗: $e');
      rethrow;
    }
  }

  /// 通過 REST API 刪除用戶
  static Future<void> _deleteUserViaRestApi(String userId) async {
    try {
      final url = Uri.parse(
        '${FirebaseConfigWindows.firestoreBaseUrl}/projects/${FirebaseConfigWindows.projectId}/databases/(default)/documents/$_collection/$userId'
      );

      final response = await http.delete(url);

      if (response.statusCode != 200 && response.statusCode != 404) {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      logger.e('REST API 刪除用戶失敗: $e');
      rethrow;
    }
  }

  // ==================== SDK 實作 ====================

  /// 通過 SDK 獲取用戶
  static Future<Map<String, dynamic>?> _getUserViaSDK(String userId) async {
    try {
      final docRef = _firestore.collection(_collection).doc(userId);
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        return docSnapshot.data();
      }
      return null;
    } on FirebaseException catch (e) {
      logger.e('Firestore 錯誤 - 獲取用戶失敗: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      logger.e('SDK 獲取用戶失敗: $e');
      rethrow;
    }
  }

  /// 通過 SDK 創建用戶
  static Future<void> _createUserViaSDK(String userId, Map<String, dynamic> data) async {
    try {
      final docRef = _firestore.collection(_collection).doc(userId);
      await docRef.set(data);
    } on FirebaseException catch (e) {
      logger.e('Firestore 錯誤 - 創建用戶失敗: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      logger.e('SDK 創建用戶失敗: $e');
      rethrow;
    }
  }

  /// 通過 SDK 更新用戶
  static Future<void> _updateUserViaSDK(String userId, Map<String, dynamic> data) async {
    try {
      final docRef = _firestore.collection(_collection).doc(userId);
      await docRef.update(data);
    } on FirebaseException catch (e) {
      logger.e('Firestore 錯誤 - 更新用戶失敗: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      logger.e('SDK 更新用戶失敗: $e');
      rethrow;
    }
  }

  /// 通過 SDK 刪除用戶
  static Future<void> _deleteUserViaSDK(String userId) async {
    try {
      final docRef = _firestore.collection(_collection).doc(userId);
      await docRef.delete();
    } on FirebaseException catch (e) {
      logger.e('Firestore 錯誤 - 刪除用戶失敗: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      logger.e('SDK 刪除用戶失敗: $e');
      rethrow;
    }
  }

  // ==================== Firestore 欄位轉換 ====================

  /// 將 Firestore REST API 欄位轉換為 Dart Map
  static Map<String, dynamic> _convertFirestoreFields(Map<String, dynamic> fields) {
    final result = <String, dynamic>{};

    for (final entry in fields.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is Map<String, dynamic>) {
        if (value.containsKey('stringValue')) {
          result[key] = value['stringValue'];
        } else if (value.containsKey('integerValue')) {
          result[key] = int.tryParse(value['integerValue']) ?? 0;
        } else if (value.containsKey('booleanValue')) {
          result[key] = value['booleanValue'];
        } else if (value.containsKey('timestampValue')) {
          result[key] = DateTime.parse(value['timestampValue']);
        } else if (value.containsKey('nullValue')) {
          result[key] = null;
        }
      }
    }

    return result;
  }

  /// 將 Dart Map 轉換為 Firestore REST API 欄位格式
  static Map<String, dynamic> _convertToFirestoreFields(Map<String, dynamic> data) {
    final result = <String, dynamic>{};

    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value == null) {
        result[key] = {'nullValue': null};
      } else if (value is String) {
        result[key] = {'stringValue': value};
      } else if (value is int) {
        result[key] = {'integerValue': value.toString()};
      } else if (value is bool) {
        result[key] = {'booleanValue': value};
      } else if (value is DateTime) {
        result[key] = {'timestampValue': value.toUtc().toIso8601String()};
      } else if (value is FieldValue) {
        // 處理伺服器時間戳
        result[key] = {'timestampValue': DateTime.now().toUtc().toIso8601String()};
      }
    }

    return result;
  }
}
