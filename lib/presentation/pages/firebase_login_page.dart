import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../../presentation/themes/app_theme.dart';
import '../../core/utils/logger_utils.dart';
import '../../data/services/api/firebase_auth_service.dart';
import '../../shared/widgets/auth_error_dialog.dart';

/// Firebase 多種登入方式頁面
class FirebaseLoginPage extends StatefulWidget {
  const FirebaseLoginPage({super.key});

  @override
  State<FirebaseLoginPage> createState() => _FirebaseLoginPageState();
}

class _FirebaseLoginPageState extends State<FirebaseLoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _displayNameController = TextEditingController();
  
  bool _isLoading = false;
  bool _isLoginMode = true;
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _displayNameController.dispose();
    super.dispose();
  }

  /// 電子郵件登入/註冊
  Future<void> _handleEmailAuth() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isLoginMode) {
        // 登入
        final user = await FirebaseAuthService.signInWithEmailAndPassword(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        );
        
        if (user != null && mounted) {
          _showSuccessMessage('登入成功！歡迎回來，${user.displayName ?? user.email}');

          // 等待一小段時間確保狀態更新完成
          await Future.delayed(const Duration(milliseconds: 500));

          // 再次檢查 mounted 狀態
          if (mounted) {
            Navigator.of(context).pop(user);
          }
        }
      } else {
        // 註冊
        final user = await FirebaseAuthService.registerWithEmailAndPassword(
          email: _emailController.text.trim(),
          password: _passwordController.text,
          displayName: _displayNameController.text.trim().isEmpty 
              ? null 
              : _displayNameController.text.trim(),
        );
        
        if (user != null && mounted) {
          _showSuccessMessage('註冊成功！歡迎加入，${user.displayName ?? user.email}');

          // 等待一小段時間確保狀態更新完成
          await Future.delayed(const Duration(milliseconds: 500));

          // 再次檢查 mounted 狀態
          if (mounted) {
            Navigator.of(context).pop(user);
          }
        }
      }
    } catch (e) {
      logger.e('電子郵件認證失敗: $e');
      if (mounted) {
        // 使用新的錯誤對話框
        await showDialog<void>(
          context: context,
          barrierDismissible: false,
          builder: (context) => AuthErrorDialog(
            title: _isLoginMode ? '登入失敗' : '註冊失敗',
            message: e.toString(),
            email: _emailController.text.trim(),
            onRetry: () => _handleEmailAuth(),
            showDiagnostics: kDebugMode,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Google 登入
  Future<void> _handleGoogleSignIn() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final user = await FirebaseAuthService.signInWithGoogle();
      
      if (user != null && mounted) {
        _showSuccessMessage('Google 登入成功！歡迎，${user.displayName ?? user.email}');

        // 等待一小段時間確保狀態更新完成
        await Future.delayed(const Duration(milliseconds: 500));

        // 再次檢查 mounted 狀態
        if (mounted) {
          Navigator.of(context).pop(user);
        }
      }
    } catch (e) {
      logger.e('Google 登入失敗: $e');
      if (mounted) {
        _showErrorMessage('Google 登入失敗：${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Apple 登入
  Future<void> _handleAppleSignIn() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final user = await FirebaseAuthService.signInWithApple();
      
      if (user != null && mounted) {
        _showSuccessMessage('Apple 登入成功！歡迎，${user.displayName ?? user.email}');
        Navigator.of(context).pop(user);
      }
    } catch (e) {
      logger.e('Apple 登入失敗: $e');
      if (mounted) {
        _showErrorMessage('Apple 登入失敗：${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 匿名登入
  Future<void> _handleAnonymousSignIn() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final user = await FirebaseAuthService.signInAnonymously();
      
      if (user != null && mounted) {
        _showSuccessMessage('匿名登入成功！您可以開始使用應用程式');

        // 等待一小段時間確保狀態更新完成
        await Future.delayed(const Duration(milliseconds: 500));

        // 再次檢查 mounted 狀態
        if (mounted) {
          if (Navigator.of(context).canPop()) {
            // 有上一頁，正常返回
            Navigator.of(context).pop(user);
          } else {
            // 沒有上一頁，導航到主畫面（確保顯示 NavigationBar）
            Navigator.of(context).pushNamedAndRemoveUntil(
              '/main',
              (route) => false,
            );
          }
        }
      }
    } catch (e) {
      logger.e('匿名登入失敗: $e');
      if (mounted) {
        _showErrorMessage('匿名登入失敗：${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 忘記密碼
  Future<void> _handleForgotPassword() async {
    if (_emailController.text.trim().isEmpty) {
      _showErrorMessage('請先輸入您的電子郵件地址');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await FirebaseAuthService.sendPasswordResetEmail(
        email: _emailController.text.trim(),
      );
      
      if (mounted) {
        _showSuccessMessage('密碼重置郵件已發送到您的信箱');
      }
    } catch (e) {
      logger.e('發送密碼重置郵件失敗: $e');
      if (mounted) {
        _showErrorMessage('發送失敗：${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 顯示成功訊息
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.successGreen,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 顯示錯誤訊息
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isLoginMode ? '登入' : '註冊'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Logo 或標題
                  Image.asset(
                    'assets/images/flutter_launcher_icons.png',
                    width: 80,
                    height: 80,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'AstReal',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: AppColors.royalIndigo,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _isLoginMode ? '歡迎回來' : '加入我們',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 32),

                  // 電子郵件表單
                  _buildEmailForm(),
                  const SizedBox(height: 24),

                  // 社交登入分隔線
                  Row(
                    children: [
                      const Expanded(child: Divider()),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          '或使用以下方式',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ),
                      const Expanded(child: Divider()),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // 社交登入按鈕
                  _buildSocialLoginButtons(),
                  const SizedBox(height: 24),

                  // 匿名登入
                  _buildAnonymousLoginButton(),
                  const SizedBox(height: 16),

                  // 切換登入/註冊模式
                  _buildModeToggle(),
                ],
              ),
            ),
    );
  }

  /// 構建電子郵件表單
  Widget _buildEmailForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // 顯示名稱（僅註冊時）
          if (!_isLoginMode) ...[
            TextFormField(
              controller: _displayNameController,
              decoration: const InputDecoration(
                labelText: '顯示名稱（選填）',
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // 電子郵件
          TextFormField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            decoration: const InputDecoration(
              labelText: '電子郵件',
              prefixIcon: Icon(Icons.email),
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '請輸入電子郵件';
              }
              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                return '請輸入有效的電子郵件格式';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // 密碼
          TextFormField(
            controller: _passwordController,
            obscureText: _obscurePassword,
            decoration: InputDecoration(
              labelText: '密碼',
              prefixIcon: const Icon(Icons.lock),
              suffixIcon: IconButton(
                icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              ),
              border: const OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '請輸入密碼';
              }
              if (!_isLoginMode && value.length < 6) {
                return '密碼至少需要 6 個字符';
              }
              return null;
            },
          ),
          const SizedBox(height: 24),

          // 登入/註冊按鈕
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _handleEmailAuth,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.royalIndigo,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                _isLoginMode ? '登入' : '註冊',
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ),

          // 忘記密碼（僅登入時）
          if (_isLoginMode) ...[
            const SizedBox(height: 8),
            TextButton(
              onPressed: _handleForgotPassword,
              child: const Text('忘記密碼？'),
            ),
          ],
        ],
      ),
    );
  }

  /// 構建社交登入按鈕
  Widget _buildSocialLoginButtons() {
    return Column(
      children: [
        // Google 登入
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _handleGoogleSignIn,
            icon: const Icon(Icons.g_mobiledata, color: Colors.red),
            label: const Text('使用 Google 登入'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),

        // Apple 登入（僅 iOS/macOS）
        if (!kIsWeb && (Platform.isIOS || Platform.isMacOS)) ...[
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _handleAppleSignIn,
              icon: const Icon(Icons.apple, color: Colors.black),
              label: const Text('使用 Apple 登入'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
        ],
      ],
    );
  }

  /// 構建匿名登入按鈕
  Widget _buildAnonymousLoginButton() {
    return SizedBox(
      width: double.infinity,
      child: TextButton.icon(
        onPressed: _handleAnonymousSignIn,
        icon: const Icon(Icons.person_outline),
        label: const Text('匿名登入（稍後可升級帳戶）'),
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  /// 構建模式切換
  Widget _buildModeToggle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(_isLoginMode ? '還沒有帳戶？' : '已經有帳戶？'),
        TextButton(
          onPressed: () {
            setState(() {
              _isLoginMode = !_isLoginMode;
              _formKey.currentState?.reset();
            });
          },
          child: Text(_isLoginMode ? '立即註冊' : '立即登入'),
        ),
      ],
    );
  }
}
