import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/services/api/auth_service.dart';
import '../../../data/services/api/interpretation_credits_service.dart';
import '../firebase_login_page.dart';
import 'astrology_guidance_page.dart';

/// 解讀服務頁面 - App Store 合規版本
/// 完全移除購買相關功能，改為帳號登入和功能解鎖模式
class PurchaseInterpretationPage extends StatefulWidget {
  const PurchaseInterpretationPage({super.key});

  @override
  State<PurchaseInterpretationPage> createState() =>
      _PurchaseInterpretationPageState();
}

class _PurchaseInterpretationPageState
    extends State<PurchaseInterpretationPage> {
  bool _isLoading = true;

  // 用戶狀態
  bool _isPremium = false;
  int _totalCredits = 0;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 載入資料
  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // 載入用戶解讀次數狀態
      final creditsDetails = await InterpretationCreditsService.getCreditsDetails();

      setState(() {
        _isPremium = creditsDetails['isPremium'] ?? false;
        _totalCredits = creditsDetails['totalCredits'] ?? 0;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeManager.getBackgroundColorForPage(PageType.settings),
      appBar: AppBar(
        title: const Text('解讀服務'),
        backgroundColor: ThemeManager.getPrimaryColorForPage(PageType.settings),
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // 主要內容區域
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 統一的主要內容區塊
                        _buildMainContentSection(),
                        // const SizedBox(height: 24),

                        // 使用注意事項
                        // _buildAstrologyGuidanceSection(),

                        // 底部留白，為置底按鈕預留空間
                        // const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),

                // 置底的登入按鈕區域
                _buildBottomLoginSection(),
              ],
            ),
    );
  }

  /// 構建主要內容區塊
  Widget _buildMainContentSection() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 標題區域
            Row(
              children: [
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    color: ThemeManager.getPrimaryColorForPage(PageType.settings).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    Icons.auto_awesome,
                    color: ThemeManager.getPrimaryColorForPage(PageType.settings),
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '深入剖析服務',
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: ThemeManager.getTextColorForPage(PageType.settings),
                        ),
                      ),
                      Text(
                        '專業星盤解讀與個人化建議',
                        style: TextStyle(
                          fontSize: 15,
                          color: ThemeManager.getTextColorForPage(PageType.settings, isSecondary: true),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // 當前狀態顯示
            _buildCurrentStatusIndicator(),

            const SizedBox(height: 24),

            // 服務說明
            _buildServiceDescription(),

            const SizedBox(height: 24),

            // 使用步驟
            _buildUsageSteps(),

            const SizedBox(height: 24),
            // 使用注意事項
            _buildAstrologyGuidanceSection(),
          ],
        ),
      ),
    );
  }

  /// 構建當前狀態指示器
  Widget _buildCurrentStatusIndicator() {
    final isSignedIn = AuthService.isSignedIn();

    final settingsTheme = PageType.settings;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isSignedIn
          ? ThemeManager.getCardColorForPage(settingsTheme)
          : ThemeManager.getSurfaceColorForPage(settingsTheme),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSignedIn
            ? ThemeManager.getPrimaryColorForPage(settingsTheme).withValues(alpha: 0.3)
            : ThemeManager.getTextColorForPage(settingsTheme, isSecondary: true).withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            isSignedIn ? Icons.check_circle : Icons.info_outline,
            color: isSignedIn
              ? ThemeManager.getFunctionalColorForPage(settingsTheme, FunctionalColorType.success)
              : ThemeManager.getPrimaryColorForPage(PageType.settings),
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isSignedIn ? '帳號已登入' : '需要登入帳號',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isSignedIn
                      ? ThemeManager.getFunctionalColorForPage(settingsTheme, FunctionalColorType.success)
                      : ThemeManager.getTextColorForPage(settingsTheme),
                  ),
                ),
                Text(
                  isSignedIn
                    ? (_isPremium ? '完整功能已啟用' : '請在官網啟用完整功能')
                    : '登入後即可使用深入剖析服務',
                  style: TextStyle(
                    fontSize: 14,
                    color: ThemeManager.getTextColorForPage(settingsTheme, isSecondary: true),
                  ),
                ),
              ],
            ),
          ),
          if (_totalCredits > 0) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: ThemeManager.getPrimaryColorForPage(settingsTheme),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                '$_totalCredits 次',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 構建服務說明
  Widget _buildServiceDescription() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '服務內容',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: ThemeManager.getTextColorForPage(PageType.settings),
          ),
        ),
        const SizedBox(height: 12),

        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: ThemeManager.getSurfaceColorForPage(PageType.settings),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: ThemeManager.getTextColorForPage(PageType.settings, isSecondary: true).withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildServiceItem(Icons.auto_awesome, '深度星盤分析', '專業解讀您的出生星盤'),
              const SizedBox(height: 12),
              _buildServiceItem(Icons.timeline, '流年運勢預測', '了解未來趨勢變化'),
              const SizedBox(height: 12),
              _buildServiceItem(Icons.psychology, '個人化建議', '針對性的人生指導'),
              const SizedBox(height: 12),
              _buildServiceItem(Icons.support, '持續更新', '定期優化解讀內容'),
            ],
          ),
        ),
      ],
    );
  }

  /// 構建使用步驟
  Widget _buildUsageSteps() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '使用步驟',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: ThemeManager.getTextColorForPage(PageType.settings),
          ),
        ),
        const SizedBox(height: 12),

        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: ThemeManager.getCardColorForPage(PageType.settings),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: ThemeManager.getTextColorForPage(PageType.settings, isSecondary: true).withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStepItem('1', '註冊或登入個人帳號'),
              const SizedBox(height: 8),
              _buildStepItem('2', '聯繫客服以開通進階功能資格'),
              const SizedBox(height: 8),
              _buildStepItem('3', '依指示完成授權啟用'),
              const SizedBox(height: 8),
              _buildStepItem('4', '立即體驗完整的占星剖析服務'),
            ],
          ),
        ),
      ],
    );
  }

  /// 構建置底登入區域
  Widget _buildBottomLoginSection() {
    final isSignedIn = AuthService.isSignedIn();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeManager.getCardColorForPage(PageType.settings),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!isSignedIn) ...[
              // 登入提示
              Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: ThemeManager.getAccentColorForPage(PageType.settings),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '需要登入帳號才能使用深入剖析服務',
                      style: TextStyle(
                        fontSize: 14,
                        color: ThemeManager.getTextColorForPage(PageType.settings, isSecondary: true),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // 登入按鈕
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: _navigateToLogin,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ThemeManager.getPrimaryColorForPage(PageType.settings),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.login, size: 20),
                      SizedBox(width: 8),
                      Text(
                        '登入帳號',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ] else ...[
              // 已登入狀態
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: ThemeManager.getSurfaceColorForPage(PageType.settings),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: ThemeManager.getFunctionalColorForPage(PageType.settings, FunctionalColorType.success).withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: ThemeManager.getFunctionalColorForPage(PageType.settings, FunctionalColorType.success),
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '帳號已登入',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: ThemeManager.getFunctionalColorForPage(PageType.settings, FunctionalColorType.success),
                            ),
                          ),
                          Text(
                            '您可以開始使用深入剖析服務',
                            style: TextStyle(
                              fontSize: 14,
                              color: ThemeManager.getTextColorForPage(PageType.settings, isSecondary: true),
                            ),
                          ),
                        ],
                      ),
                    ),
                    TextButton(
                      onPressed: _refreshAccountStatus,
                      child: Text(
                        '重新整理',
                        style: TextStyle(
                          fontSize: 14,
                          color: ThemeManager.getFunctionalColorForPage(PageType.settings, FunctionalColorType.success),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 構建步驟項目
  Widget _buildStepItem(String number, String description) {
    return Row(
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: ThemeManager.getPrimaryColorForPage(PageType.settings),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(
              number,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            description,
            style: TextStyle(
              fontSize: 14,
              color: ThemeManager.getTextColorForPage(PageType.settings, isSecondary: true),
            ),
          ),
        ),
      ],
    );
  }

  /// 構建服務項目
  Widget _buildServiceItem(IconData icon, String title, String description) {
    return Row(
      children: [
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: ThemeManager.getAccentColorForPage(PageType.settings).withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: ThemeManager.getPrimaryColorForPage(PageType.settings),
            size: 18,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                  color: ThemeManager.getTextColorForPage(PageType.settings),
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: TextStyle(
                  fontSize: 13,
                  color: ThemeManager.getTextColorForPage(PageType.settings, isSecondary: true),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 構建使用注意事項區域
  Widget _buildAstrologyGuidanceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '使用注意事項',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: ThemeManager.getTextColorForPage(PageType.settings),
          ),
        ),
        const SizedBox(height: 12),
        StyledCard(
          child: Padding(
            padding: const EdgeInsets.all(0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: ThemeManager.getFunctionalColorForPage(PageType.settings, FunctionalColorType.info).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.info_outline,
                        color: ThemeManager.getFunctionalColorForPage(PageType.settings, FunctionalColorType.info),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '占星分析注意事項',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: ThemeManager.getTextColorForPage(PageType.settings),
                            ),
                          ),
                          Text(
                            '了解使用原則與重要提醒',
                            style: TextStyle(
                              fontSize: 14,
                              color: ThemeManager.getTextColorForPage(PageType.settings, isSecondary: true),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  '在使用占星分析功能前，請先了解相關的使用原則、準確性說明和重要注意事項，以確保正確理解和使用分析結果。',
                  style: TextStyle(
                    fontSize: 14,
                    color: ThemeManager.getTextColorForPage(PageType.settings, isSecondary: true),
                    height: 1.4,
                  ),
                ),

                const SizedBox(height: 16),

                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AstrologyGuidancePage(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ThemeManager.getFunctionalColorForPage(PageType.settings, FunctionalColorType.info),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 0),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      '查看注意事項',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 導航到登入頁面
  void _navigateToLogin() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FirebaseLoginPage(),
      ),
    ).then((_) {
      // 登入後重新載入狀態
      _loadData();
    });
  }

  /// 重新整理帳號狀態
  void _refreshAccountStatus() {
    _loadData();
  }
}
