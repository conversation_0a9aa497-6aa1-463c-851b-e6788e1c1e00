import 'package:flutter/material.dart' hide DatePickerTheme;
import 'package:flutter/services.dart';
import 'package:uuid/uuid.dart';

import '../../../shared/utils/geocoding_service.dart';
import '../../astreal.dart';
import '../../data/services/api/birth_data_service.dart';
import '../../shared/widgets/common/enhanced_date_time_picker.dart';
import '../../shared/widgets/common/responsive_wrapper.dart';

/// 出生資料表單頁面，用於創建或編輯出生資料
class BirthDataFormPage extends StatefulWidget {
  final BirthData? initialData;

  const BirthDataFormPage({Key? key, this.initialData}) : super(key: key);

  @override
  State<BirthDataFormPage> createState() => _BirthDataFormPageState();
}

class _BirthDataFormPageState extends State<BirthDataFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _placeController = TextEditingController();
  final _notesController = TextEditingController();
  final _dateController = TextEditingController();
  final _timeController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();
  bool _isLoading = false;
  ChartCategory _selectedCategory = ChartCategory.personal;
  Gender? _selectedGender; // 性別（非必填）
  bool _isTimeUncertain = false; // 出生時間是否不確定

  // 地理位置信息
  double? _latitude;
  double? _longitude;
  String? _timezone;
  String? _locationError; // 地點錯誤訊息
  bool _isLocationLoading = false; // 地理編碼載入狀態

  @override
  void initState() {
    super.initState();

    // 初始化日期和時間控制器
    _updateTextControllers();

    // 如果有初始數據，則填充表單
    if (widget.initialData != null) {
      _nameController.text = widget.initialData!.name;
      _placeController.text = widget.initialData!.birthPlace;
      _notesController.text = widget.initialData!.notes ?? '';
      _selectedDate = widget.initialData!.dateTime;
      _selectedTime = TimeOfDay(
        hour: widget.initialData!.dateTime.hour,
        minute: widget.initialData!.dateTime.minute,
      );
      _selectedCategory = widget.initialData!.category; // 設置類別
      _selectedGender = widget.initialData!.gender; // 設置性別
      _isTimeUncertain = widget.initialData!.isTimeUncertain; // 設置出生時間不確定狀態
      _latitude = widget.initialData!.latitude; // 設置緯度
      _longitude = widget.initialData!.longitude; // 設置經度

      // 更新日期和時間控制器
      _updateTextControllers();

      // 獲取時區信息
      _updateTimezone();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _placeController.dispose();
    _notesController.dispose();
    _dateController.dispose();
    _timeController.dispose();
    super.dispose();
  }

  // 更新日期和時間文本控制器
  void _updateTextControllers() {
    _dateController.text =
        '${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')}';
    _timeController.text =
        '${_selectedTime.hour.toString().padLeft(2, '0')}:${_selectedTime.minute.toString().padLeft(2, '0')}';
  }

  // 根據文本更新日期
  void _updateDateFromText(String text) {
    try {
      // 嘗試解析日期字符串
      final parts = text.split('-');
      if (parts.length == 3) {
        final year = int.tryParse(parts[0]);
        final month = int.tryParse(parts[1]);
        final day = int.tryParse(parts[2]);

        if (year != null && month != null && day != null) {
          // 驗證日期是否有效
          if (_isValidDate(year, month, day)) {
            // 創建新的 DateTime 對象
            final newDate = DateTime(
                year, month, day, _selectedDate.hour, _selectedDate.minute);

            setState(() {
              _selectedDate = newDate;
            });
          }
        }
      }
    } catch (e) {
      // 如果解析失敗，不做任何變更
      logger.e('解析日期失敗: $e');
    }
  }

  // 驗證日期是否有效
  bool _isValidDate(int year, int month, int day) {
    // 基本範圍檢查
    if (year < 1800 || year > DateTime.now().year + 1) return false;
    if (month < 1 || month > 12) return false;
    if (day < 1 || day > 31) return false;

    // 檢查月份天數
    final daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    // 閏年檢查
    bool isLeapYear = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    if (month == 2 && isLeapYear) {
      return day <= 29;
    }

    return day <= daysInMonth[month - 1];
  }

  // 根據文本更新時間
  void _updateTimeFromText(String text) {
    try {
      // 嘗試解析時間字符串
      final parts = text.split(':');
      if (parts.length == 2) {
        final hour = int.tryParse(parts[0]);
        final minute = int.tryParse(parts[1]);

        if (hour != null && minute != null) {
          // 驗證時間是否有效
          if (_isValidTime(hour, minute)) {
            setState(() {
              _selectedTime = TimeOfDay(hour: hour, minute: minute);
            });
          }
        }
      }
    } catch (e) {
      // 如果解析失敗，不做任何變更
      logger.e('解析時間失敗: $e');
    }
  }

  // 驗證時間是否有效
  bool _isValidTime(int hour, int minute) {
    return hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59;
  }

  // 更新時區信息
  void _updateTimezone() {
    if (_latitude != null && _longitude != null) {
      // 簡單的時區計算（基於經度）
      // 這是一個簡化的計算，實際應用中可能需要更精確的時區數據庫
      final timezoneOffset = (_longitude! / 15).round();
      final sign = timezoneOffset >= 0 ? '+' : '';
      _timezone = 'UTC$sign$timezoneOffset';
    } else {
      _timezone = null;
    }
  }

  // 統一的日期時間選擇方法
  Future<void> _selectDateTime() async {
    final currentDateTime = DateTime(
      _selectedDate.year,
      _selectedDate.month,
      _selectedDate.day,
      _selectedTime.hour,
      _selectedTime.minute,
    );

    final result = await EnhancedDateTimePicker.show(
      context: context,
      initialDateTime: currentDateTime,
      title: '選擇出生日期時間',
      minDate: DateTime(0, 1, 1),
      maxDate: DateTime.now(),
      allowTimeUncertainty: true,
      initialTimeUncertain: _isTimeUncertain,
    );

    if (result != null) {
      final selectedDateTime = result['dateTime'] as DateTime;
      final isTimeUncertain = result['isTimeUncertain'] as bool;

      setState(() {
        _selectedDate = selectedDateTime;
        _selectedTime = TimeOfDay(
          hour: selectedDateTime.hour,
          minute: selectedDateTime.minute
        );
        _isTimeUncertain = isTimeUncertain; // 保存出生時間不確定狀態
        // 更新日期和時間文本控制器
        _updateTextControllers();
      });

      // 如果時間不確定，顯示提示
      if (isTimeUncertain && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('已標記為出生時間不確定，分析結果可能會有所影響'),
            backgroundColor: Colors.orange,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  // 格式化日期時間顯示
  String _formatDateTime(DateTime date, TimeOfDay time) {
    return '${date.year}年${date.month}月${date.day}日 ${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  // 獲取當前位置
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLocationLoading = true;
    });

    try {
      // 獲取當前位置的經緯度
      final coordinates = await GeocodingService.getCurrentLocation();

      if (coordinates == null) {
        if (mounted) {
          setState(() {
            _locationError = '無法獲取當前位置，請確保已開啟位置服務和權限';
            _isLocationLoading = false;
          });
        }
        return;
      }

      // 嘗試將經緯度轉換為地址
      final address = await GeocodingService.getAddressFromCoordinates(
        coordinates['latitude']!,
        coordinates['longitude']!,
      );

      if (address != null && mounted) {
        setState(() {
          _placeController.text = address;
          _latitude = coordinates['latitude'];
          _longitude = coordinates['longitude'];
          _locationError = null; // 清除錯誤訊息
          _isLocationLoading = false;
        });
        _updateTimezone();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已獲取當前位置: $address'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // 如果無法獲取地址，但有經緯度，則直接使用經緯度字符串
        if (mounted) {
          final locationStr =
              '緯度: ${coordinates['latitude']!.toStringAsFixed(6)}, 經度: ${coordinates['longitude']!.toStringAsFixed(6)}';
          setState(() {
            _placeController.text = locationStr;
            _latitude = coordinates['latitude'];
            _longitude = coordinates['longitude'];
            _locationError = null; // 清除錯誤訊息
            _isLocationLoading = false;
          });
          _updateTimezone();

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('無法獲取地址名稱，已使用經緯度: $locationStr'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _locationError = '獲取位置失敗: ${e.toString()}';
        _isLocationLoading = false;
      });
    }
  }

  // 保存出生資料
  Future<void> _saveBirthData() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 先確保日期和時間已經更新
    _updateDateFromText(_dateController.text);
    _updateTimeFromText(_timeController.text);

    setState(() {
      _isLoading = true;
    });

    try {
      // 檢查是否已有經緯度資訊，如果沒有則嘗試獲取
      Map<String, double> coordinates;

      if (_latitude != null && _longitude != null) {
        // 使用已有的經緯度
        coordinates = {
          'latitude': _latitude!,
          'longitude': _longitude!,
        };
      } else {
        // 嘗試從地址獲取經緯度
        try {
          coordinates = await GeocodingService.getCoordinatesFromAddress(
            _placeController.text,
          );
        } catch (e) {
          if (mounted) {
            setState(() {
              _locationError = e.toString();
              _isLoading = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('無法獲取出生地的經緯度：${e.toString()}')),
            );
          }
          return;
        }
      }

      // 創建出生日期時間
      final birthDateTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _selectedTime.hour,
        _selectedTime.minute,
      );

      // 創建或更新出生資料
      final birthData = BirthData(
        id: widget.initialData?.id ?? const Uuid().v4(),
        name: _nameController.text,
        dateTime: birthDateTime,
        birthPlace: _placeController.text,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        latitude: coordinates['latitude']!,
        longitude: coordinates['longitude']!,
        category: _selectedCategory, // 添加類別信息
        gender: _selectedGender, // 添加性別信息
        isTimeUncertain: _isTimeUncertain, // 添加出生時間不確定狀態
      );

      // 直接保存到系統中
      final birthDataService = BirthDataService();
      await birthDataService.addBirthData(birthData);

      // 顯示成功訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.initialData == null ? '出生資料已新增' : '出生資料已更新'),
            backgroundColor: AppColors.successGreen,
          ),
        );

        // 返回創建或更新的出生資料
        Navigator.of(context).pop(birthData);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存出生資料時出錯: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isNarrowScreen = screenWidth < 600;

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.person_add,
                color: AppColors.royalIndigo,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              widget.initialData == null ? '新增出生資料' : '編輯出生資料',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                letterSpacing: 0.5,
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? _buildLoadingWidget()
          : Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppColors.scaffoldBackground,
                    AppColors.scaffoldBackground.withValues(alpha: 0.8),
                  ],
                ),
              ),
              child: ResponsiveFormWrapper(
                maxWidth: 600.0, // 表單適合較小的寬度
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  padding: ResponsiveUtils.getResponsivePadding(context),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 頁面標題和說明
                        // _buildPageHeader(),
                        // const SizedBox(height: 16),

                        // 基本資料（整合姓名和類別）
                        _buildBasicInfoSection(),
                        // const SizedBox(height: 16),

                        // 出生資訊（整合日期時間和地點）
                        // _buildBirthInfoSection(isNarrowScreen),
                        // const SizedBox(height: 16),

                        // 備註和保存（整合在一起）
                        // _buildNotesAndSaveSection(),
                        // const SizedBox(height: 16),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    );
  }

  /// 構建基本資料區塊（整合姓名和類別）
  Widget _buildBasicInfoSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.1),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.royalIndigo.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 區塊標題
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.person_outline,
                  color: AppColors.royalIndigo,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                '基本資料',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // 姓名輸入
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.royalIndigo.withValues(alpha: 0.3),
              ),
              color: Colors.grey[50],
            ),
            child: TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                hintText: '請輸入姓名（例：王小明）',
                hintStyle: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                prefixIcon: Icon(
                  Icons.person_outline,
                  color: AppColors.royalIndigo.withValues(alpha: 0.7),
                  size: 20,
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '請輸入姓名';
                }
                if (value.isEmpty) {
                  return '姓名至少需要1個字符';
                }
                return null;
              },
            ),
          ),
          const SizedBox(height: 16),

          // 出生資訊
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.solarAmber.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.event_available,
                  color: AppColors.solarAmber,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                '出生資訊',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // 日期時間選擇
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.solarAmber.withValues(alpha: 0.3),
              ),
              color: Colors.grey[50],
            ),
            child: ListTile(
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              leading: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: AppColors.solarAmber.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: const Icon(
                  Icons.calendar_month,
                  color: AppColors.solarAmber,
                  size: 18,
                ),
              ),
              title: Text(
                _formatDateTime(_selectedDate, _selectedTime),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.royalIndigo,
                ),
              ),
              // subtitle: const Text(
              //   '點擊選擇出生日期和時間',
              //   style: TextStyle(fontSize: 11),
              // ),
              trailing: Icon(
                Icons.arrow_forward_ios,
                color: AppColors.solarAmber.withValues(alpha: 0.7),
                size: 14,
              ),
              onTap: _selectDateTime,
            ),
          ),
          const SizedBox(height: 16),

          // 出生地輸入
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.royalIndigo.withValues(alpha: 0.3),
              ),
              color: Colors.grey[50],
            ),
            child: TextFormField(
              controller: _placeController,
              decoration: InputDecoration(
                hintText: '請輸入出生地（例：台北市中正區）',
                hintStyle: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                prefixIcon: Icon(
                  Icons.location_on_outlined,
                  color: AppColors.royalIndigo.withValues(alpha: 0.7),
                  size: 20,
                ),
                suffixIcon: _isLocationLoading
                    ? Container(
                        margin: const EdgeInsets.all(12),
                        width: 20,
                        height: 20,
                        child: const CircularProgressIndicator(
                          strokeWidth: 2,
                        ),
                      )
                    : Container(
                        margin: const EdgeInsets.only(right: 8),
                        child: IconButton(
                          icon: Icon(
                            Icons.my_location,
                            color: AppColors.solarAmber.withValues(alpha: 0.8),
                            size: 18,
                          ),
                          onPressed: _getCurrentLocation,
                          tooltip: '取得當前位置',
                        ),
                      ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '請輸入出生地';
                }
                if (value.isEmpty) {
                  return '出生地至少需要1個字符';
                }
                return null;
              },
              onChanged: (value) async {
                // 當用戶輸入地址時，嘗試獲取經緯度
                if (value.isNotEmpty && value.length > 1) {
                  setState(() {
                    _isLocationLoading = true;
                  });

                  try {
                    final coordinates = await GeocodingService.getCoordinatesFromAddress(value);
                    if (mounted) {
                      setState(() {
                        _latitude = coordinates['latitude'];
                        _longitude = coordinates['longitude'];
                        _locationError = null; // 清除錯誤訊息
                        _isLocationLoading = false;
                      });
                      _updateTimezone();
                    }
                  } catch (e) {
                    if (mounted) {
                      setState(() {
                        _latitude = null;
                        _longitude = null;
                        _timezone = null;
                        _locationError = e.toString(); // 保存錯誤訊息
                        _isLocationLoading = false;
                      });
                    }
                  }
                } else {
                  // 如果輸入太短，清除經緯度和錯誤訊息
                  setState(() {
                    _latitude = null;
                    _longitude = null;
                    _timezone = null;
                    _locationError = null;
                  });
                }
              },
            ),
          ),

          // 錯誤訊息顯示
          if (_locationError != null) ...[
            const SizedBox(height: 8),
            _buildLocationError(),
          ],

          // 經緯度和時區信息顯示
          if (_latitude != null && _longitude != null) ...[
            const SizedBox(height: 8),
            _buildLocationInfo(),
          ],

          const SizedBox(height: 16),

          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.indigoLight.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.note_outlined,
                  color: AppColors.indigoLight,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                '備註（選填）',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.royalIndigo.withValues(alpha: 0.3),
              ),
              color: Colors.grey[50],
            ),
            child: TextFormField(
              controller: _notesController,
              decoration: InputDecoration(
                hintText: '請輸入備註（例：雙胞胎、剖腹產時間等）',
                hintStyle: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                prefixIcon: Icon(
                  Icons.note_outlined,
                  color: AppColors.royalIndigo.withValues(alpha: 0.7),
                  size: 20,
                ),
              ),
              maxLines: 2,
              minLines: 2,
            ),
          ),

          const SizedBox(height: 16),

          // 性別選擇
          const Text(
            '性別（選填）',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.royalIndigo,
            ),
          ),
          const SizedBox(height: 8),
          _buildGenderSelection(),
          const SizedBox(height: 16),

          // 類別選擇
          Row(
            children: [
              const Text(
                '類別',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.royalIndigo,
                ),
              ),
              const Spacer(),
              Text(
                '左右滑動查看更多',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[500],
                  fontStyle: FontStyle.italic,
                ),
              ),
              const SizedBox(width: 4),
              Icon(
                Icons.swipe_left,
                size: 14,
                color: Colors.grey[500],
              ),
            ],
          ),
          const SizedBox(height: 8),
          _buildCategorySelection(),

          const SizedBox(height: 16),
          // 保存按鈕
          Container(
            width: double.infinity,
            height: 52,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.royalIndigo,
                  AppColors.royalIndigo.withValues(alpha: 0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(14),
              boxShadow: [
                BoxShadow(
                  color: AppColors.royalIndigo.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: _saveBirthData,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(14),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.save,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.initialData == null ? '新增出生資料' : '保存修改',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建類別選擇
  Widget _buildCategorySelection() {
    return Container(
      height: 40, // 固定高度
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          // 主要的滑動列表
          ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            itemCount: ChartCategory.allCategories.length,
            itemBuilder: (context, index) {
              final category = ChartCategory.allCategories[index];
              final isSelected = _selectedCategory == category;

              return Container(
                margin: EdgeInsets.only(
                  right: index < ChartCategory.allCategories.length - 1 ? 8 : 0,
                ),
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedCategory = category;
                    });
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? category.color.withValues(alpha: 0.15)
                          : Colors.grey[100],
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected
                            ? category.color
                            : Colors.grey[300]!,
                        width: isSelected ? 2 : 1,
                      ),
                      boxShadow: isSelected ? [
                        BoxShadow(
                          color: category.color.withValues(alpha: 0.3),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ] : null,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          category.icon,
                          size: 18,
                          color: isSelected
                              ? category.color
                              : Colors.grey[600],
                        ),
                        const SizedBox(width: 8),
                        Text(
                          category.displayName,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                            color: isSelected
                                ? category.color
                                : Colors.grey[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),

          // 右側漸變提示
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            child: Container(
              width: 20,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    Colors.white.withValues(alpha: 0.0),
                    Colors.white.withValues(alpha: 0.8),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建地點錯誤訊息顯示
  Widget _buildLocationError() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.red.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red[600],
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _locationError!,
              style: TextStyle(
                fontSize: 12,
                color: Colors.red[700],
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _locationError = null;
              });
            },
            child: Text(
              '關閉',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red[600],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建位置信息顯示
  Widget _buildLocationInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: Colors.blue[600],
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                '地理位置資訊',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      Icons.my_location,
                      color: Colors.grey[600],
                      size: 14,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '經緯度:',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        '${_latitude!.toStringAsFixed(4)}, ${_longitude!.toStringAsFixed(4)}',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[800],
                          fontFamily: 'monospace',
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Row(
                children: [
                  Icon(
                    Icons.schedule,
                    color: Colors.grey[600],
                    size: 14,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '時區:',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _timezone ?? 'N/A',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey[800],
                      fontFamily: 'monospace',
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建性別選擇
  Widget _buildGenderSelection() {
    return Row(
      children: [
        // 添加「未選擇」選項
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _selectedGender = null;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
              decoration: BoxDecoration(
                color: _selectedGender == null
                    ? Colors.grey.withValues(alpha: 0.15)
                    : Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _selectedGender == null
                      ? Colors.grey[600]!
                      : Colors.grey[300]!,
                  width: _selectedGender == null ? 2 : 1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.help_outline,
                    size: 16,
                    color: _selectedGender == null
                        ? Colors.grey[700]
                        : Colors.grey[500],
                  ),
                  const SizedBox(width: 4),
                  Flexible(
                    child: Text(
                      '未選擇',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: _selectedGender == null ? FontWeight.w600 : FontWeight.normal,
                        color: _selectedGender == null
                            ? Colors.grey[700]
                            : Colors.grey[600],
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // 性別選項
        ...Gender.allGenders.map((gender) {
          final isSelected = _selectedGender == gender;
          return Expanded(
            child: Container(
              margin: const EdgeInsets.only(left: 6),
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedGender = gender;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? gender.color.withValues(alpha: 0.15)
                        : Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected
                          ? gender.color
                          : Colors.grey[300]!,
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        gender.icon,
                        size: 16,
                        color: isSelected
                            ? gender.color
                            : Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Flexible(
                        child: Text(
                          gender.displayName,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                            color: isSelected
                                ? gender.color
                                : Colors.grey[700],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ],
    );
  }

  /// 構建Loading組件
  Widget _buildLoadingWidget() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.scaffoldBackground,
            AppColors.scaffoldBackground.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  width: 80,
                  height: 80,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppColors.royalIndigo.withValues(alpha: 0.3),
                    ),
                  ),
                ),
                const SizedBox(
                  width: 50,
                  height: 50,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppColors.royalIndigo,
                    ),
                  ),
                ),
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: AppColors.solarAmber,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.solarAmber.withValues(alpha: 0.3),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.person_add,
                    color: Colors.white,
                    size: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            const Text(
              '正在保存出生資料...',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColors.royalIndigo,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '正在驗證地點資訊並建立檔案',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 日期輸入格式化器
class DateInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    // 只允許數字和連字符
    final filteredText = text.replaceAll(RegExp(r'[^0-9-]'), '');

    // 自動添加連字符
    String formattedText = '';
    int digitCount = 0;

    for (int i = 0; i < filteredText.length; i++) {
      final char = filteredText[i];

      if (char == '-') {
        if (formattedText.isNotEmpty && !formattedText.endsWith('-')) {
          formattedText += char;
        }
      } else {
        // 數字
        if (digitCount == 4 && !formattedText.contains('-')) {
          formattedText += '-';
        } else if (digitCount == 6 && formattedText.split('-').length == 2) {
          formattedText += '-';
        }

        if (digitCount < 8) {
          // 限制最多8位數字 (YYYY-MM-DD)
          formattedText += char;
          digitCount++;
        }
      }
    }

    // 限制總長度為10 (YYYY-MM-DD)
    if (formattedText.length > 10) {
      formattedText = formattedText.substring(0, 10);
    }

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: formattedText.length),
    );
  }
}

/// 時間輸入格式化器
class TimeInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    // 只允許數字和冒號
    final filteredText = text.replaceAll(RegExp(r'[^0-9:]'), '');

    // 自動添加冒號
    String formattedText = '';
    int digitCount = 0;

    for (int i = 0; i < filteredText.length; i++) {
      final char = filteredText[i];

      if (char == ':') {
        if (formattedText.isNotEmpty &&
            !formattedText.endsWith(':') &&
            digitCount >= 2) {
          formattedText += char;
        }
      } else {
        // 數字
        if (digitCount == 2 && !formattedText.contains(':')) {
          formattedText += ':';
        }

        if (digitCount < 4) {
          // 限制最多4位數字 (HH:MM)
          formattedText += char;
          digitCount++;
        }
      }
    }

    // 限制總長度為5 (HH:MM)
    if (formattedText.length > 5) {
      formattedText = formattedText.substring(0, 5);
    }

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: formattedText.length),
    );
  }
}
