import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

import '../../astreal.dart';
import '../../data/services/api/interpretation_record_service.dart';
import 'interpretation_record_detail_page.dart';

/// 解讀紀錄列表頁面
class InterpretationRecordsPage extends StatefulWidget {
  const InterpretationRecordsPage({super.key});

  @override
  State<InterpretationRecordsPage> createState() => _InterpretationRecordsPageState();
}

class _InterpretationRecordsPageState extends State<InterpretationRecordsPage> {
  List<InterpretationRecord> _records = [];
  List<InterpretationRecord> _filteredRecords = [];
  bool _isLoading = true;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadRecords();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('解讀紀錄'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
            tooltip: '搜索紀錄',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.file_download_rounded, color: AppColors.royalIndigo),
                    SizedBox(width: 8),
                    Text('匯出解讀紀錄'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'import',
                child: Row(
                  children: [
                    Icon(Icons.file_upload_rounded, color: AppColors.solarAmber),
                    SizedBox(width: 8),
                    Text('匯入解讀紀錄'),
                  ],
                ),
              ),
              const PopupMenuDivider(),
              const PopupMenuItem(
                value: 'clear_all',
                child: Row(
                  children: [
                    Icon(Icons.clear_all_rounded, color: Colors.red),
                    SizedBox(width: 8),
                    Text('清空所有紀錄'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.royalIndigo),
        ),
      );
    }

    if (_records.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // 搜索欄
        if (_searchQuery.isNotEmpty) _buildSearchBar(),
        
        // 統計信息
        _buildStatistics(),
        
        // 記錄列表
        Expanded(
          child: _buildRecordsList(),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              '暫無解讀紀錄',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '開始使用解讀功能，您的解讀紀錄將會顯示在這裡',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: '搜索解讀紀錄...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              _searchController.clear();
              _clearSearch();
            },
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onChanged: _performSearch,
      ),
    );
  }

  Widget _buildStatistics() {
    final totalRecords = _records.length;
    final filteredCount = _filteredRecords.length;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(
            Icons.analytics,
            color: AppColors.royalIndigo,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            _searchQuery.isEmpty 
                ? '共 $totalRecords 條紀錄'
                : '找到 $filteredCount 條紀錄（共 $totalRecords 條）',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordsList() {
    final records = _searchQuery.isEmpty ? _records : _filteredRecords;
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: records.length,
      itemBuilder: (context, index) {
        final record = records[index];
        return _buildRecordCard(record);
      },
    );
  }

  Widget _buildRecordCard(InterpretationRecord record) {
    return StyledCard(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      onTap: () => _navigateToDetail(record),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  record.interpretationTypeIcon,
                  style: const TextStyle(fontSize: 20),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        record.title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      // Text(
                      //   record.interpretationTypeDisplayName,
                      //   style: const TextStyle(
                      //     fontSize: 12,
                      //     color: AppColors.royalIndigo,
                      //     fontWeight: FontWeight.w500,
                      //   ),
                      // ),
                    ],
                  ),
                ),
                Text(
                  record.formattedCreatedAt,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              record.contentPreview,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
                height: 1.4,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                _buildInfoChip(
                  icon: Icons.person,
                  label: record.primaryPersonName,
                  color: Colors.blue,
                ),
                if (record.secondaryPersonName != null) ...[
                  const SizedBox(width: 8),
                  _buildInfoChip(
                    icon: Icons.people,
                    label: record.secondaryPersonName!,
                    color: Colors.green,
                  ),
                ],
                const SizedBox(width: 8),
                _buildInfoChip(
                  icon: Icons.category,
                  label: record.chartType,
                  color: Colors.orange,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _loadRecords() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final records = await InterpretationRecordService.getAllRecords();
      setState(() {
        _records = records;
        _filteredRecords = records;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('載入紀錄失敗: $e')),
        );
      }
    }
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('搜索解讀紀錄'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: '輸入關鍵字...',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
          onSubmitted: (value) {
            Navigator.of(context).pop();
            _performSearch(value);
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performSearch(_searchController.text);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.royalIndigo,
              foregroundColor: Colors.white,
            ),
            child: const Text('搜索'),
          ),
        ],
      ),
    );
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });

    if (query.trim().isEmpty) {
      setState(() {
        _filteredRecords = _records;
      });
      return;
    }

    InterpretationRecordService.searchRecords(query).then((results) {
      setState(() {
        _filteredRecords = results;
      });
    });
  }

  void _clearSearch() {
    setState(() {
      _searchQuery = '';
      _filteredRecords = _records;
    });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportRecords();
        break;
      case 'import':
        _importRecords();
        break;
      case 'clear_all':
        _showClearAllDialog();
        break;
    }
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清空所有紀錄'),
        content: const Text('確定要清空所有解讀紀錄嗎？此操作無法復原。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _clearAllRecords();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('清空'),
          ),
        ],
      ),
    );
  }

  Future<void> _clearAllRecords() async {
    try {
      await InterpretationRecordService.clearAllRecords();
      await _loadRecords();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('所有紀錄已清空'),
            backgroundColor: AppColors.royalIndigo,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('清空失敗: $e')),
        );
      }
    }
  }

  /// 匯出解讀紀錄
  Future<void> _exportRecords() async {
    if (_records.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('沒有可匯出的解讀紀錄'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      // 顯示載入對話框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在匯出解讀紀錄...'),
            ],
          ),
        ),
      );

      // 準備匯出數據
      final exportData = {
        'version': '1.0',
        'exportDate': DateTime.now().toIso8601String(),
        'totalRecords': _records.length,
        'records': _records.map((record) => record.toJson()).toList(),
      };

      // 轉換為 JSON 字符串
      final jsonString = const JsonEncoder.withIndent('  ').convert(exportData);

      // 獲取臨時目錄
      final directory = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'astreal_interpretation_records_$timestamp.json';
      final file = File('${directory.path}/$fileName');

      // 寫入文件
      await file.writeAsString(jsonString);

      // 關閉載入對話框
      if (mounted) Navigator.of(context).pop();

      // 分享文件
      await Share.shareXFiles(
        [XFile(file.path)],
        text: '星盤解讀紀錄匯出文件',
        subject: 'AstReal 解讀紀錄 - ${DateTime.now().year}/${DateTime.now().month}/${DateTime.now().day}',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('成功匯出 ${_records.length} 條解讀紀錄'),
            backgroundColor: AppColors.royalIndigo,
            action: SnackBarAction(
              label: '複製路徑',
              textColor: Colors.white,
              onPressed: () {
                Clipboard.setData(ClipboardData(text: file.path));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('文件路徑已複製到剪貼板')),
                );
              },
            ),
          ),
        );
      }
    } catch (e) {
      // 關閉載入對話框
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('匯出失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 匯入解讀紀錄
  Future<void> _importRecords() async {
    try {
      // 選擇文件 - 使用多種方式嘗試
      FilePickerResult? result;

      try {
        // 首先嘗試使用 JSON 過濾器
        result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: ['json'],
          allowMultiple: false,
        );
      } catch (e) {
        // 如果 JSON 過濾器失敗，嘗試使用任意文件類型
        try {
          result = await FilePicker.platform.pickFiles(
            type: FileType.any,
            allowMultiple: false,
          );
        } catch (e2) {
          throw Exception('文件選擇器不支援，請確保設備支援文件選擇功能');
        }
      }

      if (result == null || result.files.isEmpty) {
        return; // 用戶取消選擇
      }

      final file = result.files.first;
      if (file.path == null) {
        throw Exception('無法讀取選擇的文件');
      }

      // 驗證文件類型
      final fileName = file.name.toLowerCase();
      if (!fileName.endsWith('.json')) {
        final shouldContinue = await _showFileTypeWarningDialog(fileName);
        if (!shouldContinue) return;
      }

      // 顯示載入對話框
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('正在匯入解讀紀錄...'),
              ],
            ),
          ),
        );
      }

      // 讀取文件內容
      final fileContent = await File(file.path!).readAsString();
      final jsonData = jsonDecode(fileContent) as Map<String, dynamic>;

      // 驗證文件格式
      if (!jsonData.containsKey('records') || !jsonData.containsKey('version')) {
        throw Exception('無效的解讀紀錄文件格式');
      }

      final recordsData = jsonData['records'] as List<dynamic>;
      final importRecords = <InterpretationRecord>[];

      // 解析紀錄
      for (final recordData in recordsData) {
        try {
          final record = InterpretationRecord.fromJson(recordData as Map<String, dynamic>);
          importRecords.add(record);
        } catch (e) {
          // 跳過無效的紀錄
          continue;
        }
      }

      if (importRecords.isEmpty) {
        throw Exception('文件中沒有有效的解讀紀錄');
      }

      // 關閉載入對話框
      if (mounted) Navigator.of(context).pop();

      // 顯示匯入確認對話框
      final shouldImport = await _showImportConfirmDialog(
        importRecords.length,
        jsonData['exportDate'] as String?,
      );

      if (!shouldImport) return;

      // 顯示載入對話框
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('正在保存解讀紀錄...'),
              ],
            ),
          ),
        );
      }

      // 保存紀錄
      int successCount = 0;
      for (final record in importRecords) {
        try {
          await InterpretationRecordService.saveRecord(record);
          successCount++;
        } catch (e) {
          // 跳過保存失敗的紀錄
          continue;
        }
      }

      // 關閉載入對話框
      if (mounted) Navigator.of(context).pop();

      // 重新載入紀錄
      await _loadRecords();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('成功匯入 $successCount 條解讀紀錄'),
            backgroundColor: AppColors.royalIndigo,
          ),
        );
      }
    } catch (e) {
      // 關閉載入對話框
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        String errorMessage = '匯入失敗';

        // 根據錯誤類型提供更友好的錯誤信息
        if (e.toString().contains('FilePicker') || e.toString().contains('PlatformException')) {
          errorMessage = '文件選擇器錯誤，請嘗試重新選擇文件或使用其他文件管理器';
        } else if (e.toString().contains('FormatException') || e.toString().contains('jsonDecode')) {
          errorMessage = '文件格式錯誤，請確認選擇的是有效的 JSON 文件';
        } else if (e.toString().contains('無效的解讀紀錄文件格式')) {
          errorMessage = '文件內容格式不正確，請選擇正確的解讀紀錄文件';
        } else if (e.toString().contains('文件中沒有有效的解讀紀錄')) {
          errorMessage = '文件中沒有找到有效的解讀紀錄數據';
        } else if (e.toString().contains('文件選擇器不支援')) {
          errorMessage = '設備不支援文件選擇功能，請檢查應用權限';
        } else {
          errorMessage = '匯入失敗：${e.toString()}';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: '重試',
              textColor: Colors.white,
              onPressed: _importRecords,
            ),
          ),
        );
      }
    }
  }

  /// 顯示匯入確認對話框
  Future<bool> _showImportConfirmDialog(int recordCount, String? exportDate) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.file_upload_rounded, color: AppColors.solarAmber),
            SizedBox(width: 8),
            Text('確認匯入'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('即將匯入 $recordCount 條解讀紀錄'),
            if (exportDate != null) ...[
              const SizedBox(height: 8),
              Text(
                '匯出時間：${_formatImportDate(exportDate)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning_rounded, color: Colors.orange.shade600, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '重複的紀錄將會被覆蓋',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.solarAmber,
              foregroundColor: Colors.white,
            ),
            child: const Text('確認匯入'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// 顯示文件類型警告對話框
  Future<bool> _showFileTypeWarningDialog(String fileName) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning_rounded, color: Colors.orange),
            SizedBox(width: 8),
            Text('文件類型警告'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('選擇的文件不是 JSON 格式：'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                fileName,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              '解讀紀錄文件應該是 .json 格式。繼續可能會導致匯入失敗。',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.orange,
            ),
            child: const Text('仍要繼續'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// 格式化匯入日期
  String _formatImportDate(String isoDate) {
    try {
      final date = DateTime.parse(isoDate);
      return '${date.year}/${date.month}/${date.day} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return isoDate;
    }
  }

  void _navigateToDetail(InterpretationRecord record) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InterpretationRecordDetailPage(record: record),
      ),
    ).then((_) {
      // 返回時重新載入紀錄，以防有刪除操作
      _loadRecords();
    });
  }
}
