import 'dart:async';

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../core/utils/logger_utils.dart';
import '../../data/models/video/video_data.dart';
import '../../presentation/themes/app_theme.dart';
import '../../shared/widgets/web_aware_app_bar.dart';
import '../../shared/widgets/web_aware_pop_scope.dart';

/// YouTube 影片播放器頁面
/// 使用 WebView 在應用內播放 YouTube 影片
class VideoPlayerPage extends StatefulWidget {
  final VideoData video;

  const VideoPlayerPage({
    super.key,
    required this.video,
  });

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  late WebViewController _webViewController;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  Timer? _loadingTimer;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  @override
  void dispose() {
    _loadingTimer?.cancel();
    super.dispose();
  }

  /// 初始化 WebView
  void _initializeWebView() {
    logger.d('初始化 WebView 播放器：${widget.video.title}');

    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.black)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            logger.d('開始載入頁面：$url');
            if (mounted) {
              setState(() {
                _isLoading = true;
                _hasError = false;
              });

              // 設置載入超時
              _loadingTimer?.cancel();
              _loadingTimer = Timer(const Duration(seconds: 30), () {
                if (mounted && _isLoading) {
                  logger.w('影片載入超時');
                  setState(() {
                    _isLoading = false;
                    _hasError = true;
                    _errorMessage = '影片載入超時，請檢查網路連接或嘗試重新載入';
                  });
                }
              });
            }
          },
          onPageFinished: (String url) {
            logger.d('頁面載入完成：$url');
            _loadingTimer?.cancel();

            // 延遲一點時間確保內容完全載入
            Future.delayed(const Duration(milliseconds: 1500), () {
              if (mounted) {
                setState(() {
                  _isLoading = false;
                });
              }
            });
          },
          onWebResourceError: (WebResourceError error) {
            logger.e('WebView 載入錯誤：${error.description}');
            if (mounted) {
              setState(() {
                _isLoading = false;
                _hasError = true;
                _errorMessage = error.description;
              });
            }
          },
          onNavigationRequest: (NavigationRequest request) {
            logger.d('導航請求：${request.url}');

            // 允許所有 YouTube 和 Google 相關的 URL
            if (request.url.contains('youtube.com') ||
                request.url.contains('youtu.be') ||
                request.url.contains('googlevideo.com') ||
                request.url.contains('google.com') ||
                request.url.startsWith('data:') ||
                request.url.startsWith('about:blank')) {
              return NavigationDecision.navigate;
            }

            logger.w('阻止導航到：${request.url}');
            return NavigationDecision.prevent;
          },
        ),
      );

    // 載入 YouTube 嵌入頁面
    _loadYouTubeVideo();
  }

  /// 載入 YouTube 影片
  void _loadYouTubeVideo() {
    try {
      // 嘗試直接載入 YouTube 嵌入 URL
      final embedUrl = _buildEmbedUrl();
      logger.d('載入 YouTube 嵌入 URL：$embedUrl');

      // 先嘗試直接載入 URL
      _webViewController.loadRequest(Uri.parse(embedUrl)).catchError((error) {
        logger.w('直接載入失敗，嘗試使用 HTML：$error');
        // 如果直接載入失敗，使用 HTML 包裝
        final htmlContent = _buildHtmlContent(embedUrl);
        _webViewController.loadHtmlString(htmlContent);
      });
    } catch (e) {
      logger.e('載入 YouTube 影片失敗：$e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = '載入影片失敗：$e';
        });
      }
    }
  }

  /// 構建嵌入 URL
  String _buildEmbedUrl() {
    final baseUrl = 'https://www.youtube.com/embed/${widget.video.youtubeId}';

    // 使用更簡單的參數，避免可能的載入問題
    final parameters = <String, String>{
      'rel': '0', // 不顯示相關影片
      'modestbranding': '1', // 簡化 YouTube 品牌
      'playsinline': '1', // 內嵌播放
    };

    final queryString = parameters.entries
        .map((entry) => '${entry.key}=${entry.value}')
        .join('&');

    return '$baseUrl?$queryString';
  }

  /// 構建 HTML 內容
  String _buildHtmlContent(String embedUrl) {
    // 轉義標題和描述中的特殊字符
    final safeTitle = widget.video.title
        .replaceAll('"', '&quot;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;');
    final safeDescription = widget.video.description
        .replaceAll('"', '&quot;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;');

    return '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="description" content="$safeDescription">
    <title>$safeTitle</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        html, body {
            width: 100%;
            height: 100%;
            background-color: #000;
            overflow: hidden;
        }
        .video-container {
            position: relative;
            width: 100%;
            height: 100%;
        }
        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="video-container">
        <iframe
            src="$embedUrl"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowfullscreen
            loading="lazy">
        </iframe>
    </div>
    <script>
        // 確保頁面完全載入後通知父頁面
        window.addEventListener('load', function() {
            console.log('YouTube iframe loaded');
        });

        // 監聽 iframe 載入事件
        document.querySelector('iframe').addEventListener('load', function() {
            console.log('YouTube iframe content loaded');
        });
    </script>
</body>
</html>
    ''';
  }

  @override
  Widget build(BuildContext context) {
    return WebAwarePopScope(
      routeName: '/video_player',
      child: Scaffold(
        appBar: WebAwareAppBarHelper.simple(
          title: widget.video.title,
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white),
              onPressed: _hasError ? _reloadVideo : null,
              tooltip: '重新載入',
            ),
            PopupMenuButton<String>(
              onSelected: _handleMenuAction,
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'reload',
                  child: ListTile(
                    leading: Icon(Icons.refresh),
                    title: Text('重新載入'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'open_youtube',
                  child: ListTile(
                    leading: Icon(Icons.open_in_new),
                    title: Text('在 YouTube 中開啟'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'share',
                  child: ListTile(
                    leading: Icon(Icons.share),
                    title: Text('分享'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ],
        ),
        body: Column(
          children: [
            // 影片播放區域
            Expanded(
              child: _buildVideoPlayer(),
            ),

            // 影片資訊
            _buildVideoInfo(),
          ],
        ),
      ),
    );
  }

  /// 構建影片播放器
  Widget _buildVideoPlayer() {
    if (_hasError) {
      return _buildErrorState();
    }

    return Stack(
      children: [
        // WebView
        WebViewWidget(controller: _webViewController),

        // 載入指示器
        if (_isLoading)
          Container(
            color: Colors.black,
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                  SizedBox(height: 16),
                  Text(
                    '載入影片中...',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  /// 構建錯誤狀態
  Widget _buildErrorState() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.white,
            ),
            const SizedBox(height: 16),
            const Text(
              '影片載入失敗',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _reloadVideo,
                  child: const Text('重試'),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _openInExternalBrowser,
                  icon: const Icon(Icons.open_in_new),
                  label: const Text('外部開啟'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 構建影片資訊
  Widget _buildVideoInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 標題
          Text(
            widget.video.title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),

          // 描述
          Text(
            widget.video.description,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 12),

          // 標籤和資訊
          Row(
            children: [
              // 時長
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  widget.video.duration,
                  style: const TextStyle(fontSize: 12),
                ),
              ),
              const SizedBox(width: 8),

              // 熱門標籤
              if (widget.video.isPopular)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.solarAmber,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    '熱門',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

              const Spacer(),

              // 發布日期
              Text(
                _formatDate(widget.video.publishedAt),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),

          // 標籤
          if (widget.video.tags.isNotEmpty) ...[
            const SizedBox(height: 12),
            Wrap(
              spacing: 6,
              runSpacing: 6,
              children: widget.video.tags.map((tag) {
                return Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    tag,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.royalIndigo,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  /// 處理選單動作
  void _handleMenuAction(String action) {
    switch (action) {
      case 'reload':
        _reloadVideo();
        break;
      case 'open_youtube':
        _openInYouTube();
        break;
      case 'share':
        _shareVideo();
        break;
    }
  }

  /// 重新載入影片
  void _reloadVideo() {
    logger.d('重新載入影片');
    setState(() {
      _hasError = false;
      _errorMessage = '';
      _isLoading = true;
    });
    _loadYouTubeVideo();
  }

  /// 在外部瀏覽器中開啟
  Future<void> _openInExternalBrowser() async {
    try {
      final url = widget.video.youtubeUrl;
      logger.d('在外部瀏覽器中開啟：$url');

      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('無法開啟外部瀏覽器')),
          );
        }
      }
    } catch (e) {
      logger.e('開啟外部瀏覽器失敗：$e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('開啟失敗：$e')),
        );
      }
    }
  }

  /// 在 YouTube 中開啟
  void _openInYouTube() {
    logger.d('在 YouTube 中開啟影片');
    // TODO: 實現在外部 YouTube 應用中開啟
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('功能開發中...')),
    );
  }

  /// 分享影片
  void _shareVideo() {
    logger.d('分享影片');
    // TODO: 實現分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('功能開發中...')),
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
  }
}
