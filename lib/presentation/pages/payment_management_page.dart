import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../core/utils/logger_utils.dart';
import '../../data/models/payment/payment_record.dart';
import '../../data/services/api/auth_service.dart';
import '../../data/services/api/firebase_payment_service.dart';
import '../../data/services/api/payment_service.dart';
import '../themes/app_theme.dart';
/// 支付管理頁面
class PaymentManagementPage extends StatefulWidget {
  const PaymentManagementPage({super.key});

  @override
  State<PaymentManagementPage> createState() => _PaymentManagementPageState();
}

class _PaymentManagementPageState extends State<PaymentManagementPage> {
  bool _isLoading = false;
  Map<String, dynamic>? _subscriptionSummary;
  List<PaymentRecord> _localPayments = [];
  List<PaymentRecord> _cloudPayments = [];

  @override
  void initState() {
    super.initState();
    _loadPaymentData();
  }

  /// 載入支付數據
  Future<void> _loadPaymentData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 獲取訂閱摘要
      final summary = await PaymentService.getSubscriptionSummary();
      
      // 獲取本地支付記錄
      final localPayments = await PaymentService.getAllPayments();
      
      // 獲取雲端支付記錄
      final cloudPayments = await FirebasePaymentService.getUserPaymentRecords();

      if (mounted) {
        setState(() {
          _subscriptionSummary = summary;
          _localPayments = localPayments;
          _cloudPayments = cloudPayments;
          _isLoading = false;
        });
      }
    } catch (e) {
      logger.e('載入支付數據失敗: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('載入支付數據失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 重新載入 Firebase 數據
  Future<void> _syncWithFirebase() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 檢查用戶是否已登入
      final currentUser = AuthService.getCurrentUser();
      if (currentUser == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('請先登入以載入支付數據'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // 重新載入數據（數據直接從 Firebase 獲取）
      await _loadPaymentData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('數據重新載入成功'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      logger.e('重新載入數據失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('重新載入數據失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('支付管理'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.sync),
            onPressed: _isLoading ? null : _syncWithFirebase,
            tooltip: '同步 Firebase',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _loadPaymentData,
            tooltip: '重新載入',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSubscriptionSummary(),
                  const SizedBox(height: 24),
                  _buildUserInfo(),
                  const SizedBox(height: 24),
                  _buildLocalPayments(),
                  const SizedBox(height: 24),
                  _buildCloudPayments(),
                ],
              ),
            ),
    );
  }

  /// 構建訂閱摘要卡片
  Widget _buildSubscriptionSummary() {
    if (_subscriptionSummary == null) {
      return const SizedBox.shrink();
    }

    final summary = _subscriptionSummary!;
    final isPremium = summary['isPremium'] as bool;
    final remainingTrials = summary['remainingTrials'] as int;
    final remainingSinglePurchases = summary['remainingSinglePurchases'] as int;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isPremium ? Icons.star : Icons.star_border,
                  color: isPremium ? AppColors.solarAmber : Colors.grey,
                ),
                const SizedBox(width: 8),
                Text(
                  '訂閱狀態',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatusRow('付費會員', isPremium ? '是' : '否', isPremium),
            _buildStatusRow('免費試用', '$remainingTrials 次', remainingTrials > 0),
            _buildStatusRow('單次購買', '$remainingSinglePurchases 次', remainingSinglePurchases > 0),
            _buildStatusRow('總支付記錄', '${summary['totalPayments']} 筆', true),
          ],
        ),
      ),
    );
  }

  /// 構建狀態行
  Widget _buildStatusRow(String label, String value, bool isActive) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isActive ? AppColors.successGreen.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              value,
              style: TextStyle(
                color: isActive ? AppColors.successGreen : Colors.grey,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建用戶信息
  Widget _buildUserInfo() {
    final user = AuthService.getCurrentUser();
    String formatted = DateFormat('yyyy-MM-dd HH:mm:ss').format(user!.createdAt!);
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, color: AppColors.royalIndigo),
                const SizedBox(width: 8),
                Text(
                  '用戶信息',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...[
            _buildInfoRow('用戶 ID', user.uid),
            _buildInfoRow('電子郵件', user.email ?? '未設置'),
            _buildInfoRow('電子郵件驗證', user.emailVerified ? '已驗證' : '未驗證'),
            _buildInfoRow('註冊時間', formatted),
          ],
          ],
        ),
      ),
    );
  }

  /// 構建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建本地支付記錄
  Widget _buildLocalPayments() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.phone_android, color: AppColors.earthGreen),
                const SizedBox(width: 8),
                Text(
                  '本地支付記錄 (${_localPayments.length})',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_localPayments.isEmpty)
              const Text('沒有本地支付記錄', style: TextStyle(color: Colors.grey))
            else
              ..._localPayments.map((payment) => _buildPaymentTile(payment, false)),
          ],
        ),
      ),
    );
  }

  /// 構建雲端支付記錄
  Widget _buildCloudPayments() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.cloud, color: AppColors.cosmicPurple),
                const SizedBox(width: 8),
                Text(
                  'Firebase 支付記錄 (${_cloudPayments.length})',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_cloudPayments.isEmpty)
              const Text('沒有雲端支付記錄', style: TextStyle(color: Colors.grey))
            else
              ..._cloudPayments.map((payment) => _buildPaymentTile(payment, true)),
          ],
        ),
      ),
    );
  }

  /// 構建支付記錄項目
  Widget _buildPaymentTile(PaymentRecord payment, bool isCloud) {
    final isValid = payment.isValid && payment.expiryDate.isAfter(DateTime.now());
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(
          color: isValid ? AppColors.successGreen : Colors.grey,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
        color: isValid
            ? AppColors.successGreen.withValues(alpha: 0.05)
            : Colors.grey.withValues(alpha: 0.05),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _getPlanTypeName(payment.planType),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Row(
                children: [
                  if (isCloud)
                    const Icon(Icons.cloud, size: 16, color: AppColors.cosmicPurple),
                  const SizedBox(width: 4),
                  Text(
                    '${payment.currency} ${payment.amount.toStringAsFixed(0)}',
                    style: TextStyle(
                      color: isValid ? AppColors.successGreen : Colors.grey,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            '支付時間：${_formatDateTime(payment.paymentDate)}',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          Text(
            '到期時間：${_formatDateTime(payment.expiryDate)}',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          Text(
            '交易 ID：${payment.transactionId}',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// 獲取方案類型名稱
  String _getPlanTypeName(String planType) {
    switch (planType) {
      case 'single':
        return '單次解讀';
      case 'monthly':
        return '月度方案';
      case 'quarterly':
        return '季度方案';
      case 'yearly':
        return '年度方案';
      default:
        return planType;
    }
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
