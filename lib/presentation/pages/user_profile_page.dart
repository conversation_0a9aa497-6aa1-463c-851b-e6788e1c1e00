import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

import '../../astreal.dart';
import '../../data/services/api/account_deletion_service.dart';
import '../../data/services/api/firebase_auth_service.dart';
import '../../data/services/api/payment_service.dart';
import 'admin/admin_dashboard_page.dart';
import 'firebase_login_page.dart';
import 'payment_management_page.dart';

/// 用戶資料頁面
class UserProfilePage extends StatefulWidget {
  const UserProfilePage({super.key});

  @override
  State<UserProfilePage> createState() => _UserProfilePageState();
}

class _UserProfilePageState extends State<UserProfilePage> {
  AppUser? _currentUser;
  bool _isLoading = false;
  Map<String, dynamic>? _subscriptionSummary;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  /// 載入用戶數據
  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final user = FirebaseAuthService.getCurrentUser();
      final summary = await PaymentService.getSubscriptionSummary();

      if (mounted) {
        setState(() {
          _currentUser = user;
          _subscriptionSummary = summary;
          _isLoading = false;
        });
      }
    } catch (e) {
      logger.e('載入用戶數據失敗: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 登入
  Future<void> _login() async {
    final result = await Navigator.of(context).push<AppUser>(
      MaterialPageRoute(
        builder: (context) => const FirebaseLoginPage(),
      ),
    );

    if (result != null) {
      await _loadUserData();
    }
  }

  /// 登出
  Future<void> _logout() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('確認登出'),
        content: const Text('您確定要登出嗎？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('登出'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await FirebaseAuthService.signOut();
        await _loadUserData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('已成功登出'),
              backgroundColor: AppColors.successGreen,
            ),
          );
        }
      } catch (e) {
        logger.e('登出失敗: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('登出失敗：$e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// 發送電子郵件驗證
  Future<void> _sendEmailVerification() async {
    try {
      await FirebaseAuthService.sendEmailVerification();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('驗證郵件已發送'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      logger.e('發送驗證郵件失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('發送失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 更新用戶資料
  Future<void> _updateProfile() async {
    final displayNameController = TextEditingController(
      text: _currentUser?.displayName ?? '',
    );

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('更新資料'),
        content: TextField(
          controller: displayNameController,
          decoration: const InputDecoration(
            labelText: '顯示名稱',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(displayNameController.text),
            child: const Text('更新'),
          ),
        ],
      ),
    );

    if (result != null && result != _currentUser?.displayName) {
      try {
        await FirebaseAuthService.updateUserProfile(displayName: result);
        await _loadUserData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('資料更新成功'),
              backgroundColor: AppColors.successGreen,
            ),
          );
        }
      } catch (e) {
        logger.e('更新資料失敗: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('更新失敗：$e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// 複製用戶ID到剪貼簿
  Future<void> _copyUserId() async {
    if (_currentUser?.uid != null) {
      try {
        await Clipboard.setData(ClipboardData(text: _currentUser!.uid));
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('用戶 ID 已複製到剪貼簿'),
              backgroundColor: AppColors.successGreen,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } catch (e) {
        logger.e('複製用戶ID失敗: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('複製失敗：$e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// 刪除帳戶
  Future<void> _deleteAccount() async {
    // 第一次確認
    final firstConfirmed = await _showDeleteAccountWarning();
    if (!firstConfirmed) return;

    // 第二次確認（更詳細的警告）
    final secondConfirmed = await _showFinalDeleteConfirmation();
    if (!secondConfirmed) return;

    // 顯示進度對話框
    if (!mounted) return;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在刪除帳戶...'),
            SizedBox(height: 8),
            Text(
              '這可能需要一些時間，請勿關閉應用程式',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );

    try {
      // 使用新的完整刪除服務
      await AccountDeletionService.deleteUserAccount(_currentUser?.uid ?? '');

      // 關閉進度對話框
      if (mounted) {
        Navigator.of(context).pop();

        // 先回到設定頁面，然後推送登入頁面
        Navigator.of(context).popUntil((route) => route.settings.name == '/main' || route.isFirst);

        // 推送登入頁面，這樣用戶登入後可以回到設定頁面
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const FirebaseLoginPage()),
        );

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('帳戶已完全刪除'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      // 關閉進度對話框
      if (mounted) Navigator.of(context).pop();

      logger.e('刪除帳戶失敗: $e');
      if (mounted) {
        _showDeleteErrorDialog(e.toString());
      }
    }
  }

  /// 顯示刪除帳戶警告
  Future<bool> _showDeleteAccountWarning() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            SizedBox(width: 8),
            Text('刪除帳戶'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '此操作將會永久刪除：',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text('• 您的帳戶資訊'),
            const Text('• 所有出生資料'),
            const Text('• 雲端備份檔案'),
            const Text('• 購買記錄'),
            const Text('• 使用記錄'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                '⚠️ 此操作無法撤銷！',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('繼續'),
          ),
        ],
      ),
    );

    return confirmed ?? false;
  }

  /// 顯示最終刪除確認
  Future<bool> _showFinalDeleteConfirmation() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.delete_forever, color: Colors.red),
            SizedBox(width: 8),
            Text('最終確認'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '您即將永久刪除您的帳戶。',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: const Column(
                children: [
                  Icon(Icons.warning, color: Colors.red, size: 32),
                  SizedBox(height: 8),
                  Text(
                    '這個操作無法撤銷！',
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    '所有資料將被永久刪除',
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('確定刪除'),
          ),
        ],
      ),
    );

    return confirmed ?? false;
  }

  /// 顯示刪除錯誤對話框
  void _showDeleteErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('刪除失敗'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('刪除帳戶時發生錯誤：'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                error,
                style: const TextStyle(fontFamily: 'monospace'),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '請稍後再試，或聯繫客服協助。',
              style: TextStyle(color: Colors.grey.shade600),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('確定'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('用戶資料'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          if (_currentUser != null)
            IconButton(
              icon: const Icon(Icons.logout),
              onPressed: _logout,
              tooltip: '登出',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _currentUser == null
              ? _buildLoginPrompt()
              : _buildUserProfile(),
    );
  }

  /// 構建登入提示
  Widget _buildLoginPrompt() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.person_outline,
              size: 80,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              '請先登入',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '登入後可以同步您的支付記錄和設定',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _login,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.royalIndigo,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: const Text('立即登入'),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建用戶資料
  Widget _buildUserProfile() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildUserInfoCard(),
          const SizedBox(height: 16),
          _buildSubscriptionCard(),
          const SizedBox(height: 16),
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// 構建用戶信息卡片
  Widget _buildUserInfoCard() {
    String createdAt = DateFormat('yyyy-MM-dd HH:mm:ss').format(_currentUser?.createdAt! ?? DateTime.now());
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundImage: _currentUser?.photoURL != null
                      ? NetworkImage(_currentUser!.photoURL!)
                      : null,
                  child: _currentUser?.photoURL == null
                      ? Text(
                          _getInitialLetter(),
                          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                        )
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _currentUser?.displayName ?? '未設置名稱',
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      Text(
                        _currentUser?.email ?? '未設置郵件',
                        style: const TextStyle(color: Colors.grey),
                      ),
                      if (_currentUser?.isAnonymous == true)
                        const Text(
                          '匿名用戶',
                          style: TextStyle(color: Colors.orange, fontSize: 12),
                        ),
                      if (_currentUser?.isAdmin == true)
                        Container(
                          margin: const EdgeInsets.only(top: 4),
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.royalIndigo,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            '管理者',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: _updateProfile,
                  tooltip: '編輯資料',
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildCopyableInfoRow('用戶 ID', _currentUser?.uid ?? '', _copyUserId),
            _buildInfoRow('電子郵件驗證', _currentUser?.emailVerified == true ? '已驗證' : '未驗證'),
            _buildInfoRow('註冊時間', createdAt),
            
            if (_currentUser?.emailVerified == false && _currentUser?.email != null) ...[
              const SizedBox(height: 8),
              TextButton.icon(
                onPressed: _sendEmailVerification,
                icon: const Icon(Icons.email),
                label: const Text('發送驗證郵件'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 構建訂閱信息卡片
  Widget _buildSubscriptionCard() {
    if (_subscriptionSummary == null) return const SizedBox.shrink();

    final isPremium = _subscriptionSummary!['isPremium'] as bool;
    final remainingTrials = _subscriptionSummary!['remainingTrials'] as int;
    final remainingSinglePurchases = _subscriptionSummary!['remainingSinglePurchases'] as int;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isPremium ? Icons.star : Icons.star_border,
                  color: isPremium ? AppColors.solarAmber : Colors.grey,
                ),
                const SizedBox(width: 8),
                const Text(
                  '訂閱狀態',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const PaymentManagementPage(),
                      ),
                    );
                  },
                  child: const Text('管理'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            _buildInfoRow('付費會員', isPremium ? '是' : '否'),
            _buildInfoRow('免費試用', '$remainingTrials 次'),
            _buildInfoRow('單次購買', '$remainingSinglePurchases 次'),
          ],
        ),
      ),
    );
  }

  /// 構建操作按鈕
  Widget _buildActionButtons() {
    return Column(
      children: [
        // 管理者專用功能
        if (_currentUser?.isAdmin == true) ...[
          ListTile(
            leading: const Icon(Icons.admin_panel_settings, color: AppColors.royalIndigo),
            title: const Text(
              '管理後台',
              style: TextStyle(
                color: AppColors.royalIndigo,
                fontWeight: FontWeight.bold,
              ),
            ),
            trailing: const Icon(Icons.chevron_right, color: AppColors.royalIndigo),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AdminDashboardPage(),
                ),
              );
            },
          ),
          const Divider(),
        ],

        ListTile(
          leading: const Icon(Icons.payment),
          title: const Text('支付管理'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const PaymentManagementPage(),
              ),
            );
          },
        ),
        const Divider(),
        ListTile(
          leading: const Icon(Icons.delete_forever, color: Colors.red),
          title: const Text('刪除帳戶', style: TextStyle(color: Colors.red)),
          trailing: const Icon(Icons.chevron_right),
          onTap: _deleteAccount,
        ),
      ],
    );
  }

  /// 構建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建可複製的信息行
  Widget _buildCopyableInfoRow(String label, String value, VoidCallback onCopy) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: GestureDetector(
              onTap: value.isNotEmpty ? onCopy : null,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 4),
                // decoration: value.isNotEmpty
                //     ? BoxDecoration(
                //         color: Colors.grey.withValues(alpha: 0.1),
                //         borderRadius: BorderRadius.circular(4),
                //         border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                //       )
                //     : null,
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.isNotEmpty ? value : '未設置',
                        style: TextStyle(
                          color: value.isNotEmpty ? AppColors.royalIndigo : Colors.grey,
                          fontSize: 12,
                          fontFamily: 'monospace', // 使用等寬字體顯示ID
                        ),
                      ),
                    ),
                    if (value.isNotEmpty) ...[
                      const SizedBox(width: 8),
                      Icon(
                        Icons.copy,
                        size: 16,
                        color: AppColors.royalIndigo.withValues(alpha: 0.7),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 安全地獲取用戶初始字母
  String _getInitialLetter() {
    // 嘗試從 displayName 獲取
    final displayName = _currentUser?.displayName;
    if (displayName != null && displayName.isNotEmpty) {
      return displayName.substring(0, 1).toUpperCase();
    }

    // 嘗試從 email 獲取
    final email = _currentUser?.email;
    if (email != null && email.isNotEmpty) {
      return email.substring(0, 1).toUpperCase();
    }

    // 如果是匿名用戶，返回 'A'
    if (_currentUser?.isAnonymous == true) {
      return 'A';
    }

    // 默認返回 'U'
    return 'U';
  }
}
