import '../../../data/models/astrology/chart_data.dart';
import '../../../data/models/astrology/chart_type.dart';
import '../../../data/models/astrology/planet_position.dart';
import '../copy_options.dart';
import 'chart_info_calculator.dart';
import 'chart_info_formatter.dart';

/// 行星信息生成器
///
/// 負責生成行星位置、尊貴力量、日夜區分等信息
class PlanetInfoGenerator {
  /// 生成行星位置信息
  static String generatePlanetPositions(
      ChartData chartData, CopyOptions options) {
    if (!options.includePlanetPositions || chartData.planets == null) return '';

    final StringBuffer text = StringBuffer();
    final chartType = chartData.chartType;
    final primaryPerson = chartData.primaryPerson;
    final secondaryPerson = chartData.secondaryPerson;

    text.writeln(ChartInfoFormatter.sectionTitle('行星'));

    // 合盤特殊說明
    switch (chartType) {
      case ChartType.synastry:
        text.writeln('${secondaryPerson!.name}的行星落入${primaryPerson.name}的宮位');
        break;
      case ChartType.transit:
      case ChartType.solarArcDirection:
        text.writeln('${chartType.displayName}的行星落入${primaryPerson.name}的宮位');
        break;
      default:
        break;
    }

    // 預先計算需要的數據
    final isDaytime =
        ChartInfoCalculator.calculateDaytimeStatus(chartData.planets);
    final planetToHousesRuled =
        ChartInfoCalculator.calculatePlanetToHousesRuled(chartData);

    // 生成每個行星的信息
    for (final planet in chartData.planets!) {
      // 跳過阿拉伯點，它們將在特殊點部分顯示
      // if (planet.id >= 100) continue;

      final planetInfo = _generateSinglePlanetInfo(
        chartData,
        planet,
        planetToHousesRuled,
        isDaytime,
      );
      text.writeln(planetInfo);
    }

    return text.toString();
  }

  /// 生成行星尊貴力量信息
  static String generatePlanetDignities(
      ChartData chartData, CopyOptions options) {
    if (!options.includePlanetDignities || chartData.planets == null) return '';

    final StringBuffer text = StringBuffer();
    text.writeln(ChartInfoFormatter.sectionTitle('行星尊貴力量'));

    for (final planet in chartData.planets!) {
      // 只顯示主要行星（太陽到土星）的尊貴力量
      if (planet.id <= 6 && planet.dignity != PlanetDignity.peregrine) {
        text.writeln(
            '${planet.name}：${planet.getDignityText()} (在${planet.sign})');
      }
    }

    return text.toString();
  }

  /// 生成行星日夜區分信息
  static String generatePlanetSectStatus(
      ChartData chartData, CopyOptions options) {
    if (!options.includePlanetSectStatus || chartData.planets == null)
      return '';

    final isDaytime =
        ChartInfoCalculator.calculateDaytimeStatus(chartData.planets);
    if (isDaytime == null) return '';

    final StringBuffer text = StringBuffer();
    text.writeln(ChartInfoFormatter.sectionTitle('行星日夜區分'));

    for (final planet in chartData.planets!) {
      // 只顯示主要行星（太陽到土星）的日夜區分
      if (planet.id <= 6) {
        text.writeln('${planet.name}：${planet.getSectStatusText()}');
      }
    }

    return text.toString();
  }

  /// 生成單個行星的詳細信息
  static String _generateSinglePlanetInfo(
    ChartData chartData,
    PlanetPosition planet,
    Map<int, List<int>> planetToHousesRuled,
    bool? isDaytime,
  ) {
    final signDegree = planet.longitude % 30;
    final StringBuffer planetInfo = StringBuffer();

    switch (chartData.chartType) {
      case ChartType.synastry:
        planetInfo.write(ChartInfoFormatter.formatPlanetPositionDual(
          chartData.secondaryPerson!.name,
          planet.name,
          planet.sign,
          signDegree,
          planet.getHouseTextDual(chartData.primaryPerson.name),
        ));
        break;
      case ChartType.transit:
      case ChartType.solarArcDirection:
        planetInfo.write(ChartInfoFormatter.formatPlanetPositionDual(
          chartData.chartType.displayName,
          planet.name,
          planet.sign,
          signDegree,
          planet.getHouseTextDual(chartData.primaryPerson.name),
        ));
        break;
      default:
        // 基本位置信息
        planetInfo.write(ChartInfoFormatter.formatPlanetPosition(
          planet.name,
          planet.sign,
          signDegree,
          planet.getHouseText(),
        ));
        break;
    }

    // 添加逆行信息
    planetInfo
        .write(ChartInfoFormatter.formatRetrograde(planet.longitudeSpeed < 0));

    // 添加尊貴力量信息
    planetInfo.write(ChartInfoFormatter.formatDignity(
      planet.getDignityText(),
      planet.dignity != PlanetDignity.peregrine,
    ));

    // 添加宮主星信息
    if (planetToHousesRuled.containsKey(planet.id) &&
        planetToHousesRuled[planet.id]!.isNotEmpty) {
      final housesRuled = planetToHousesRuled[planet.id]!;
      planetInfo.write(ChartInfoFormatter.formatHouseRulerInfo(housesRuled));
    }

    // 添加日夜區分信息
    if (isDaytime != null && planet.id <= 6) {
      // 只為主要行星添加日夜區分信息
      planetInfo.write(ChartInfoFormatter.formatSectStatus(
        planet.getSectStatusText(),
        true,
      ));
    }

    return planetInfo.toString();
  }
}
