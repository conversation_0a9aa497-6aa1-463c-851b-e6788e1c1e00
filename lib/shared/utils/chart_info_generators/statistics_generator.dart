import '../../../astreal.dart';
import 'chart_info_calculator.dart';
import 'chart_info_formatter.dart';

/// 統計信息生成器
///
/// 負責生成元素統計、特殊點等信息
class StatisticsGenerator {
  /// 生成元素統計信息
  static String generateElementStats(ChartData chartData, CopyOptions options) {
    if (!options.includeElementStats || chartData.planets == null) return '';

    final StringBuffer text = StringBuffer();
    
    // 計算各種統計數據
    final elementStats = ChartInfoCalculator.calculateElementStats(chartData.planets!);
    final modalityStats = ChartInfoCalculator.calculateModalityStats(chartData.planets!);
    final polarityStats = ChartInfoCalculator.calculatePolarityStats(chartData.planets!);

    text.writeln(ChartInfoFormatter.sectionTitle('統計'));

    // 元素分佈（火土風水）
    _addElementStats(text, elementStats);
    
    // 品質分佈（啟動、固定、變動）
    _addModalityStats(text, modalityStats);
    
    // 陰陽分佈
    _addPolarityStats(text, polarityStats);

    // 元素行星詳細
    _addElementPlanetsDetail(text, chartData.planets!);

    return text.toString();
  }

  /// 生成特殊點（阿拉伯點）信息
  static String generateArabicPoints(ChartData chartData, CopyOptions options) {
    if (!options.includeArabicPoints ||
        chartData.arabicPoints == null ||
        chartData.arabicPoints!.isEmpty) {
      return '';
    }

    final StringBuffer text = StringBuffer();
    text.writeln(ChartInfoFormatter.sectionTitle('特殊點'));

    for (final point in chartData.arabicPoints!) {
      // 只顯示前幾個重要的阿拉伯點
      if (point.id >= AstrologyConstants.FORTUNE_POINT &&
          point.id <= AstrologyConstants.WORK_POINT) {
        final signDegree = point.longitude % 30;
        text.writeln(ChartInfoFormatter.formatArabicPoint(
          point.name,
          point.sign,
          signDegree,
          point.getHouseText(),
        ));
      }
    }

    return text.toString();
  }

  /// 添加元素統計
  static void _addElementStats(StringBuffer text, Map<String, int> elementStats) {
    text.writeln(ChartInfoFormatter.subSectionTitle('四大元素'));
    text.writeln(ChartInfoFormatter.formatStatValue('火', elementStats['火'] ?? 0));
    text.writeln(ChartInfoFormatter.formatStatValue('土', elementStats['土'] ?? 0));
    text.writeln(ChartInfoFormatter.formatStatValue('風', elementStats['風'] ?? 0));
    text.writeln(ChartInfoFormatter.formatStatValue('水', elementStats['水'] ?? 0));
  }

  /// 添加品質統計
  static void _addModalityStats(StringBuffer text, Map<String, int> modalityStats) {
    text.writeln(ChartInfoFormatter.subSectionTitle('三大性質'));
    text.writeln(ChartInfoFormatter.formatStatValue('啟動', modalityStats['啟動'] ?? 0));
    text.writeln(ChartInfoFormatter.formatStatValue('固定', modalityStats['固定'] ?? 0));
    text.writeln(ChartInfoFormatter.formatStatValue('變動', modalityStats['變動'] ?? 0));
  }

  /// 添加陰陽統計
  static void _addPolarityStats(StringBuffer text, Map<String, int> polarityStats) {
    text.writeln(ChartInfoFormatter.subSectionTitle('陰陽分佈'));
    text.writeln(ChartInfoFormatter.formatStatValue('陽性', polarityStats['陽性'] ?? 0));
    text.writeln(ChartInfoFormatter.formatStatValue('陰性', polarityStats['陰性'] ?? 0));
  }

  /// 添加元素行星詳細信息
  static void _addElementPlanetsDetail(StringBuffer text, List<PlanetPosition> planets) {
    text.writeln(ChartInfoFormatter.sectionTitle('行星元素'));

    // 元素分組詳細
    final elementPlanets = ChartInfoCalculator.getPlanetsByElement(planets);
    text.writeln(ChartInfoFormatter.formatElementPlanets('火', elementPlanets['火'] ?? []));
    text.writeln(ChartInfoFormatter.formatElementPlanets('土', elementPlanets['土'] ?? []));
    text.writeln(ChartInfoFormatter.formatElementPlanets('風', elementPlanets['風'] ?? []));
    text.writeln(ChartInfoFormatter.formatElementPlanets('水', elementPlanets['水'] ?? []));
  }
}
