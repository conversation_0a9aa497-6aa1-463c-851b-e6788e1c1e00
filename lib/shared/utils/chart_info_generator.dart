import '../../astreal.dart';
import '../../data/services/api/astrology_service.dart';
import 'chart_info_generators/aspect_info_generator.dart';
import 'chart_info_generators/basic_info_generator.dart';
import 'chart_info_generators/chart_info_calculator.dart';
import 'chart_info_generators/chart_info_formatter.dart';
import 'chart_info_generators/chart_title_generator.dart';
import 'chart_info_generators/house_info_generator.dart';
import 'chart_info_generators/planet_info_generator.dart';
import 'chart_info_generators/statistics_generator.dart';

/// 星盤資訊生成工具
///
/// 提供統一的星盤資訊生成功能，可被多個模組共用
class ChartInfoGenerator {
  /// 從 ChartData 生成星盤資訊文本
  ///
  /// [chartData] 星盤數據
  /// [options] 複製選項，控制要包含哪些信息
  /// [calculateIfNeeded] 是否在需要時自動計算星盤（預設為 true）
  static Future<String> generateFromChartData({
    required ChartData chartData,
    CopyOptions? options,
    bool calculateIfNeeded = true,
  }) async {
    // 使用預設選項
    final copyOptions = options ??
        const CopyOptions(
          includeBasicInfo: false,
          includePlanetPositions: true,
          includeHousePositions: true,
          includeAspects: true,
          includeElementStats: true,
          usePrettyFormat: false,
        );

    // 如果需要計算且數據不完整，則進行計算
    if (calculateIfNeeded && _needsCalculation(chartData)) {
      final chartViewModel =
          ChartViewModel.withChartData(initialChartData: chartData);

      // 檢查計算是否成功
      if (chartViewModel.isLoading ||
          chartViewModel.chartData.planets == null ||
          chartViewModel.chartData.planets!.isEmpty) {
        throw Exception('星盤計算失敗，請檢查出生資料是否正確');
      }

      // 使用計算後的數據
      return generateChartInfoText(chartData, copyOptions);
    } else {
      // 直接使用現有數據生成
      return generateChartInfoText(chartData, copyOptions);
    }
  }

  /// 從 ChartViewModel 生成星盤資訊文本
  ///
  /// [chartViewModel] 星盤視圖模型
  /// [options] 複製選項
  static Future<String> generateFromViewModel({
    required ChartViewModel chartViewModel,
    CopyOptions? options,
  }) async {
    final copyOptions = options ??
        const CopyOptions(
          includeBasicInfo: true,
          includePlanetPositions: true,
          includeHousePositions: true,
          includeAspects: true,
          includeElementStats: true,
          usePrettyFormat: true,
        );

    return await generateChartInfoText(chartViewModel.chartData, copyOptions);
  }

  /// 檢查是否需要計算星盤
  static bool _needsCalculation(ChartData chartData) {
    return chartData.planets == null ||
        chartData.planets!.isEmpty ||
        chartData.houses == null;
  }

  /// 獲取當前宮位制設定
  static Future<HouseSystem> getCurrentHouseSystem() async {
    try {
      final settings = await ChartSettings.loadFromPrefs();
      return settings.houseSystem;
    } catch (e) {
      logger.e('获取宫位制设置失败：$e');
      return HouseSystem.placidus; // 默认使用 Placidus 系统
    }
  }

  /// 生成包含星盤資訊的文本，用於複製到剪貼簿。
  /// 包含行星位置、宮主星、日夜區分、元素統計等信息
  ///
  /// [options] 複製選項，用於控制要包含哪些信息
  static Future<String> generateChartInfoText(
      ChartData chartData, CopyOptions options) async {
    final StringBuffer text = StringBuffer();

    // 使用各個專門的生成器來組合結果
    ChartData chartDataDual = chartData.copyWith(
      chartType: chartData.chartType,
      primaryPerson: chartData.secondaryPerson,
      secondaryPerson: chartData.primaryPerson,
      specificDate: chartData.specificDate,
    );

    if (chartData.chartType == ChartType.synastry) {
      // 重新計算
      ChartSettings chartSettings = await ChartSettings.loadFromPrefs();
      chartDataDual = await AstrologyService()
          .calculateChartData(chartDataDual, chartSettings: chartSettings);
    }

    // 1. 基本資料
    final basicInfo = BasicInfoGenerator.generateBasicInfo(chartData, options);
    text.write(basicInfo);

    // 2. 星盤標題與類型信息
    final titleInfo =
        await ChartTitleGenerator.generateTitleInfo(chartData, options);
    text.write(titleInfo);

    // 3. 行星位置信息
    final planetPositions =
        PlanetInfoGenerator.generatePlanetPositions(chartData, options);
    text.write(planetPositions);

    if (chartData.chartType == ChartType.synastry) {
      final planetPositionsDual =
          PlanetInfoGenerator.generatePlanetPositions(chartDataDual, options);
      text.write(planetPositionsDual);
    }

    // 4. 行星尊貴力量
    final planetDignities =
        PlanetInfoGenerator.generatePlanetDignities(chartData, options);
    text.write(planetDignities);

    // 5. 行星日夜區分
    final planetSectStatus =
        PlanetInfoGenerator.generatePlanetSectStatus(chartData, options);
    text.write(planetSectStatus);

    // 6. 宮位位置
    final housePositions =
        await HouseInfoGenerator.generateHousePositions(chartData, options);
    text.write(housePositions);

    // 7. 相位關係
    final aspects = AspectInfoGenerator.generateAspects(chartData, options);
    text.write(aspects);

    if (chartData.chartType == ChartType.natal) {
      // 8. 元素統計
      final elementStats =
          StatisticsGenerator.generateElementStats(chartData, options);
      text.write(elementStats);

      // 9. 特殊點（阿拉伯點）
      final arabicPoints =
          StatisticsGenerator.generateArabicPoints(chartData, options);
      text.write(arabicPoints);
    }

    return text.toString();
  }

  /// 格式化日期時間（保留向後兼容性）
  static String formatDateTime(DateTime dateTime) {
    return ChartInfoFormatter.formatDateTime(dateTime);
  }

  /// 格式化角度（保留向後兼容性）
  static String formatDegree(double degree) {
    return ChartInfoFormatter.formatDegree(degree);
  }

  /// 根據經度獲取星座（保留向後兼容性）
  static String getZodiacSign(double longitude) {
    return ChartInfoCalculator.getZodiacSign(longitude);
  }
}
