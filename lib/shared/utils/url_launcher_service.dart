import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../astreal.dart';

/// 統一的 URL 啟動服務
/// 提供一致的 URL 開啟體驗，支援應用內網頁和外部連結
class UrlLauncherService {
  /// 啟動 URL
  /// 
  /// [url] - 要開啟的 URL
  /// [context] - BuildContext，用於顯示錯誤訊息
  /// [forceExternal] - 強制使用外部應用程式開啟
  /// [showErrorSnackBar] - 是否顯示錯誤 SnackBar
  static Future<bool> launchURL(
    String url, {
    BuildContext? context,
    bool forceExternal = false,
    bool showErrorSnackBar = true,
  }) async {
    try {
      final Uri uri = Uri.parse(url);

      if (await canLaunchUrl(uri)) {
        LaunchMode mode;
        
        if (forceExternal) {
          // 強制使用外部應用程式
          mode = LaunchMode.externalApplication;
        } else if (_isInternalPage(url)) {
          // 內部頁面處理
          if (kIsWeb) {
            // Web 平台：在當前窗口中導航到相對路徑
            String pageName = _extractPageName(url);
            await launchUrl(Uri.parse(pageName), mode: LaunchMode.platformDefault);
            return true;
          } else {
            // 移動平台：在應用內 WebView 中開啟
            mode = LaunchMode.inAppWebView;
          }
        } else {
          // 外部連結：使用外部應用程式開啟
          mode = LaunchMode.externalApplication;
        }

        await launchUrl(uri, mode: mode);
        logger.i('成功啟動 URL: $url (模式: $mode)');
        return true;
      } else {
        throw Exception('無法啟動 URL: $url');
      }
    } catch (e) {
      logger.e('啟動 URL 失敗: $url, 錯誤: $e');
      
      if (showErrorSnackBar && context != null && context.mounted) {
        _showErrorSnackBar(context, '無法開啟連結，請稍後再試');
      }
      
      return false;
    }
  }

  /// 在應用內 WebView 中開啟 URL
  static Future<bool> launchInAppWebView(
    String url, {
    BuildContext? context,
    bool showErrorSnackBar = true,
  }) async {
    try {
      final Uri uri = Uri.parse(url);

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.inAppWebView);
        logger.i('在應用內 WebView 中開啟 URL: $url');
        return true;
      } else {
        throw Exception('無法啟動 URL: $url');
      }
    } catch (e) {
      logger.e('在應用內 WebView 中開啟 URL 失敗: $url, 錯誤: $e');
      
      if (showErrorSnackBar && context != null && context.mounted) {
        _showErrorSnackBar(context, '無法在應用內開啟網頁，請稍後再試');
      }
      
      return false;
    }
  }

  /// 在外部應用程式中開啟 URL
  static Future<bool> launchExternal(
    String url, {
    BuildContext? context,
    bool showErrorSnackBar = true,
  }) async {
    try {
      final Uri uri = Uri.parse(url);

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        logger.i('在外部應用程式中開啟 URL: $url');
        return true;
      } else {
        throw Exception('無法啟動 URL: $url');
      }
    } catch (e) {
      logger.e('在外部應用程式中開啟 URL 失敗: $url, 錯誤: $e');
      
      if (showErrorSnackBar && context != null && context.mounted) {
        _showErrorSnackBar(context, '無法開啟外部連結，請稍後再試');
      }
      
      return false;
    }
  }

  /// 檢查是否為內部頁面
  static bool _isInternalPage(String url) {
    return url.contains('astreal.web.app') ||
        url.contains('astreal-website.web.app') ||
        url.contains('astreal-d3f70.web.app') ||
        url.endsWith('.html');
  }

  /// 提取頁面名稱
  static String _extractPageName(String url) {
    // 如果是完整 URL，提取檔案名稱
    if (url.contains('http')) {
      Uri uri = Uri.parse(url);
      String path = uri.path;
      if (path.endsWith('.html')) {
        return path.split('/').last;
      }
    }

    // 如果已經是檔案名稱，直接返回
    if (url.endsWith('.html')) {
      return url;
    }

    // 預設返回 index.html
    return 'index.html';
  }

  /// 顯示錯誤 SnackBar
  static void _showErrorSnackBar(BuildContext context, String message) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: '確定',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// 檢查 URL 是否有效
  static bool isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// 格式化 URL（確保有協議）
  static String formatUrl(String url) {
    if (url.isEmpty) return url;
    
    // 如果沒有協議，添加 https://
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return 'https://$url';
    }
    
    return url;
  }
}
