import 'package:flutter/material.dart';

import '../../../data/models/astrology/chart_data.dart';
import '../../../data/models/astrology/chart_type.dart';
import '../../../data/models/user/birth_data.dart';
import '../../presentation/pages/chart_selection_page.dart';
import '../../presentation/themes/app_theme.dart';
import '../../presentation/viewmodels/chart_viewmodel.dart';


/// 快速星盤類型選擇器
class ChartTypeQuickSelector extends StatelessWidget {
  final ChartViewModel viewModel;
  final Function(ChartType)? onChartTypeChanged;

  const ChartTypeQuickSelector({
    Key? key,
    required this.viewModel,
    this.onChartTypeChanged,
  }) : super(key: key);

  /// 常用星盤類型列表
  static const List<ChartType> commonChartTypes = [
    ChartType.synastry,
    ChartType.mundane,
    ChartType.natal,
    ChartType.transit,
    ChartType.secondaryProgression,
    ChartType.tertiaryProgression,
    ChartType.solarReturn,
    ChartType.lunarReturn,
    ChartType.solarArcDirection,
    ChartType.firdaria,
    ChartType.composite,
    ChartType.davison,
    ChartType.marks,
    ChartType.synastrySecondary,
    ChartType.synastryTertiary,
    ChartType.compositeSecondary,
    ChartType.compositeTertiary,
    ChartType.davisonSecondary,
    ChartType.davisonTertiary,
    ChartType.marksSecondary,
    ChartType.marksTertiary,
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 30,
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Row(
        children: [
          // 可滑動的星盤類型標籤
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 0),
              itemCount: commonChartTypes.length,
              itemBuilder: (context, index) {
                final chartType = commonChartTypes[index];
                final isSelected = viewModel.chartType == chartType;

                return Container(
                  margin: const EdgeInsets.only(right: 6),
                  child: _buildChartTypeChip(
                    context,
                    chartType,
                    isSelected,
                  ),
                );
              },
            ),
          ),

          // 分隔線
          Container(
            width: 1,
            height: 30,
            color: Colors.grey.shade300,
            margin: const EdgeInsets.symmetric(horizontal: 8),
          ),

          // 更多按鈕
          _buildMoreButton(context),

          // const SizedBox(width: 12),
        ],
      ),
    );
  }

  /// 構建星盤類型標籤
  Widget _buildChartTypeChip(
    BuildContext context,
    ChartType chartType,
    bool isSelected,
  ) {
    final colorInfo = _getChartTypeColorInfo(chartType);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(18),
        onTap: () => _onChartTypeSelected(context, chartType),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: isSelected
              ? colorInfo['color'].withValues(alpha: 0.15)
              : Colors.transparent,
            borderRadius: BorderRadius.circular(18),
            border: Border.all(
              color: isSelected
                ? colorInfo['color']
                : Colors.grey.shade300,
              width: isSelected ? 1.5 : 1,
            ),
            boxShadow: isSelected ? [
              BoxShadow(
                color: colorInfo['color'].withValues(alpha: 0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ] : null,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon(
              //   colorInfo['icon'],
              //   size: 14,
              //   color: isSelected
              //     ? colorInfo['color']
              //     : Colors.grey.shade600,
              // ),
              // const SizedBox(width: 4),
              Text(
                _getShortDisplayName(chartType),
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: isSelected ? FontWeight.w700 : FontWeight.w500,
                  color: isSelected
                    ? colorInfo['color']
                    : Colors.grey.shade700,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建更多按鈕
  Widget _buildMoreButton(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(18),
        onTap: () => _showFullChartTypeSelector(context),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: AppColors.royalIndigo.withValues(alpha: 0.12),
            borderRadius: BorderRadius.circular(18),
            border: Border.all(
              color: AppColors.royalIndigo,
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.royalIndigo.withValues(alpha: 0.15),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.apps,
                size: 14,
                color: AppColors.royalIndigo,
              ),
              const SizedBox(width: 4),
              Text(
                '更多',
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w700,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 處理星盤類型選擇
  void _onChartTypeSelected(BuildContext context, ChartType chartType) async {
    if (viewModel.chartType == chartType) return;

    try {
      // 顯示載入指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // 切換星盤類型
      await viewModel.setChartType(chartType, context: context);

      // 關閉載入指示器
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // 檢查切換是否真的成功了
      if (context.mounted) {
        if (viewModel.chartType == chartType) {
          // 切換成功，顯示成功訊息
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(
                    _getChartTypeColorInfo(chartType)['icon'],
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text('已切換到：${chartType.displayName}'),
                ],
              ),
              backgroundColor: _getChartTypeColorInfo(chartType)['color'],
              duration: const Duration(seconds: 2),
            ),
          );

          // 回調通知
          onChartTypeChanged?.call(chartType);
        } else {
          // 切換未成功（可能用戶取消了），顯示取消訊息
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.info, color: Colors.white, size: 16),
                  const SizedBox(width: 8),
                  Text('已取消切換到：${chartType.displayName}'),
                ],
              ),
              backgroundColor: Colors.grey.shade600,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      // 關閉載入指示器
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤訊息
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Text('切換失敗：$e'),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// 顯示完整的星盤類型選擇器
  void _showFullChartTypeSelector(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChartSelectionPage(
          primaryPerson: viewModel.chartData.primaryPerson,
          secondaryPerson: viewModel.chartData.secondaryPerson,
          initialChartType: viewModel.chartType,
          isChangingChartType: true,
          specificDate: viewModel.chartData.specificDate,
        ),
      ),
    ).then((result) {
      // 如果用戶選擇了新的星盤資料，處理結果
      if (result != null && result is ChartData && context.mounted) {
        _handleChartDataUpdate(context, result);
      }
    });
  }

  /// 處理星盤資料更新
  Future<void> _handleChartDataUpdate(BuildContext context, ChartData newChartData) async {
    try {
      // 檢查是否真的有變動
      if (_isChartDataSame(viewModel.chartData, newChartData)) {
        debugPrint('星盤資料沒有變動，跳過更新');
        return;
      }

      // 顯示載入指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      debugPrint('開始更新星盤資料...');
      debugPrint('原星盤類型: ${viewModel.chartType}');
      debugPrint('新星盤類型: ${newChartData.chartType}');

      // 更新星盤資料
      await viewModel.updateChartData(newChartData);

      // 關閉載入指示器
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // 顯示成功訊息
      if (context.mounted) {
        final colorInfo = _getChartTypeColorInfo(newChartData.chartType);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  colorInfo['icon'],
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text('星盤已更新：${newChartData.chartType.displayName}'),
              ],
            ),
            backgroundColor: colorInfo['color'],
            duration: const Duration(seconds: 2),
          ),
        );

        // 回調通知
        onChartTypeChanged?.call(newChartData.chartType);
      }

      debugPrint('星盤資料更新完成');
    } catch (e) {
      // 關閉載入指示器
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤訊息
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('星盤更新失敗：$e')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }

      debugPrint('星盤資料更新失敗：$e');
    }
  }

  /// 檢查兩個 ChartData 是否相同
  bool _isChartDataSame(ChartData oldData, ChartData newData) {
    // 比較星盤類型
    if (oldData.chartType != newData.chartType) return false;

    // 比較主要人物
    if (oldData.primaryPerson.id != newData.primaryPerson.id) return false;

    // 比較次要人物
    if (oldData.secondaryPerson?.id != newData.secondaryPerson?.id) return false;

    // 比較特定日期
    if (oldData.specificDate != newData.specificDate) return false;

    // 比較主要人物的詳細資料
    if (!_isBirthDataSame(oldData.primaryPerson, newData.primaryPerson)) return false;

    // 比較次要人物的詳細資料
    if (oldData.secondaryPerson != null && newData.secondaryPerson != null) {
      if (!_isBirthDataSame(oldData.secondaryPerson!, newData.secondaryPerson!)) return false;
    }

    return true;
  }

  /// 檢查兩個 BirthData 是否相同
  bool _isBirthDataSame(BirthData oldData, BirthData newData) {
    return oldData.id == newData.id &&
           oldData.name == newData.name &&
           oldData.dateTime == newData.dateTime &&
           oldData.birthPlace == newData.birthPlace &&
           oldData.latitude == newData.latitude &&
           oldData.longitude == newData.longitude;
  }

  /// 獲取星盤類型的顏色和圖示資訊
  Map<String, dynamic> _getChartTypeColorInfo(ChartType chartType) {
    switch (chartType) {
      case ChartType.natal:
        return {'color': Colors.blue, 'icon': Icons.person};
      case ChartType.transit:
        return {'color': Colors.green, 'icon': Icons.timeline};
      case ChartType.synastry:
        return {'color': Colors.pink, 'icon': Icons.people};
      case ChartType.composite:
        return {'color': Colors.purple, 'icon': Icons.favorite};
      case ChartType.solarReturn:
        return {'color': Colors.orange, 'icon': Icons.wb_sunny};
      case ChartType.lunarReturn:
        return {'color': Colors.indigo, 'icon': Icons.nightlight_round};
      case ChartType.secondaryProgression:
        return {'color': Colors.teal, 'icon': Icons.trending_up};
      case ChartType.solarArcDirection:
        return {'color': Colors.amber, 'icon': Icons.arrow_circle_up};
      default:
        return {'color': Colors.grey, 'icon': Icons.circle};
    }
  }

  /// 獲取簡短的顯示名稱
  String _getShortDisplayName(ChartType chartType) {
    switch (chartType) {
      case ChartType.natal:
        return '本命盤';
      case ChartType.transit:
        return '行運盤';
      case ChartType.synastry:
        return '比較盤';
      case ChartType.composite:
        return '組合盤';
      case ChartType.solarReturn:
        return '太陽返照';
      case ChartType.lunarReturn:
        return '月亮返照';
      case ChartType.secondaryProgression:
        return '次限推運';
      case ChartType.solarArcDirection:
        return '太陽弧推運';
      default:
        return chartType.displayName;
    }
  }
}
