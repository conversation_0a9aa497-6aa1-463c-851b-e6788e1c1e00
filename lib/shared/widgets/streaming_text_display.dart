import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

import '../../core/utils/logger_utils.dart';
import '../../data/services/api/ai_streaming_service.dart';
import '../../presentation/themes/app_theme.dart';

/// 流式文字顯示 Widget
/// 支援即時顯示 AI 回應內容，就像 ChatGPT 那樣一個字一個字地出現
class StreamingTextDisplay extends StatefulWidget {
  final Stream<AIStreamingResponse> stream;
  final TextStyle? textStyle;
  final bool enableMarkdown;
  final Function(String)? onComplete;
  final Function(String)? onError;
  final Function(String)? onStatusUpdate;
  final EdgeInsetsGeometry? padding;
  final double? minHeight;

  const StreamingTextDisplay({
    super.key,
    required this.stream,
    this.textStyle,
    this.enableMarkdown = true,
    this.onComplete,
    this.onError,
    this.onStatusUpdate,
    this.padding,
    this.minHeight,
  });

  @override
  State<StreamingTextDisplay> createState() => _StreamingTextDisplayState();
}

class _StreamingTextDisplayState extends State<StreamingTextDisplay>
    with TickerProviderStateMixin {
  String _displayedContent = '';
  String _statusMessage = '';
  bool _isComplete = false;
  bool _hasError = false;
  String _errorMessage = '';
  StreamSubscription<AIStreamingResponse>? _streamSubscription;

  // 動畫控制器
  late AnimationController _cursorAnimationController;
  late Animation<double> _cursorAnimation;
  late AnimationController _fadeInController;
  late Animation<double> _fadeInAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化動畫控制器
    _cursorAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _cursorAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cursorAnimationController,
      curve: Curves.easeInOut,
    ));

    _fadeInController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeInAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeInController,
      curve: Curves.easeIn,
    ));

    // 開始光標動畫
    _cursorAnimationController.repeat(reverse: true);

    // 開始淡入動畫
    _fadeInController.forward();

    // 監聽流式響應
    _listenToStream();
  }

  @override
  void didUpdateWidget(StreamingTextDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果 stream 改變了，重新監聽
    if (oldWidget.stream != widget.stream) {
      _streamSubscription?.cancel();
      _resetState();
      _listenToStream();
    }
  }

  /// 重置狀態
  void _resetState() {
    _displayedContent = '';
    _statusMessage = '';
    _isComplete = false;
    _hasError = false;
    _errorMessage = '';

    // 重新開始光標動畫
    _cursorAnimationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _streamSubscription?.cancel();
    _cursorAnimationController.dispose();
    _fadeInController.dispose();
    super.dispose();
  }

  /// 監聽流式響應
  void _listenToStream() {
    // 確保先取消之前的訂閱
    _streamSubscription?.cancel();

    _streamSubscription = widget.stream.listen(
      (response) {
        if (!mounted) return;

        setState(() {
          switch (response.type) {
            case AIStreamingResponseType.status:
              _statusMessage = response.content;
              logger.d('狀態更新: ${response.content}');
              widget.onStatusUpdate?.call(response.content);
              break;

            case AIStreamingResponseType.content:
              // 累積內容
              if (response.fullContent != null) {
                _displayedContent = response.fullContent!;
              } else {
                _displayedContent += response.content;
              }
              _statusMessage = ''; // 清除狀態消息
              break;

            case AIStreamingResponseType.complete:
              _displayedContent = response.content;
              _isComplete = true;
              _statusMessage = '';
              _cursorAnimationController.stop();
              logger.i('流式響應完成，總內容長度: ${_displayedContent.length}');
              widget.onComplete?.call(_displayedContent);
              break;

            case AIStreamingResponseType.error:
              _hasError = true;
              _errorMessage = response.content;
              _isComplete = true;
              _statusMessage = '';
              _cursorAnimationController.stop();
              logger.e('流式響應錯誤: ${response.content}');
              widget.onError?.call(response.content);
              break;
          }
        });
      },
      onError: (error) {
        if (!mounted) return;
        
        setState(() {
          _hasError = true;
          _errorMessage = '連接錯誤：$error';
          _isComplete = true;
          _statusMessage = '';
          _cursorAnimationController.stop();
        });
        
        logger.e('流式響應流錯誤: $error');
        widget.onError?.call(_errorMessage);
      },
      onDone: () {
        if (!mounted) return;
        
        if (!_isComplete) {
          setState(() {
            _isComplete = true;
            _cursorAnimationController.stop();
          });
          widget.onComplete?.call(_displayedContent);
        }
        
        logger.d('流式響應流結束');
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeInAnimation,
      child: Container(
        constraints: widget.minHeight != null 
            ? BoxConstraints(minHeight: widget.minHeight!)
            : null,
        padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 狀態消息
            if (_statusMessage.isNotEmpty) ...[
              _buildStatusMessage(),
              const SizedBox(height: 16),
            ],

            // 錯誤消息
            if (_hasError) ...[
              _buildErrorMessage(),
            ] else ...[
              // 正常內容顯示
              _buildContent(),
            ],
          ],
        ),
      ),
    );
  }

  /// 構建狀態消息
  Widget _buildStatusMessage() {
    return Row(
      children: [
        SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              AppColors.royalIndigo.withOpacity(0.7),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            _statusMessage,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.royalIndigo.withOpacity(0.8),
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      ],
    );
  }

  /// 構建錯誤消息
  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade600,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _errorMessage,
              style: TextStyle(
                color: Colors.red.shade700,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建內容顯示
  Widget _buildContent() {
    if (_displayedContent.isEmpty && _statusMessage.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 內容顯示
        if (_displayedContent.isNotEmpty) ...[
          widget.enableMarkdown
              ? _buildMarkdownContent()
              : _buildPlainTextContent(),
        ],

        // 光標動畫（只在未完成時顯示）
        if (!_isComplete && _displayedContent.isNotEmpty) ...[
          const SizedBox(height: 4),
          _buildCursor(),
        ],
      ],
    );
  }

  /// 構建 Markdown 內容
  Widget _buildMarkdownContent() {
    return MarkdownBody(
      data: _displayedContent,
      styleSheet: MarkdownStyleSheet(
        p: widget.textStyle ?? const TextStyle(
          fontSize: 16,
          height: 1.6,
          color: AppColors.textDark,
        ),
        h1: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: AppColors.royalIndigo,
          height: 1.4,
        ),
        h2: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColors.royalIndigo,
          height: 1.4,
        ),
        h3: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.royalIndigo,
          height: 1.4,
        ),
        strong: const TextStyle(
          fontWeight: FontWeight.bold,
          color: AppColors.textDark,
        ),
        em: TextStyle(
          fontStyle: FontStyle.italic,
          color: AppColors.textDark.withOpacity(0.9),
        ),
        blockquote: TextStyle(
          color: AppColors.royalIndigo.withOpacity(0.8),
          fontStyle: FontStyle.italic,
        ),
        blockquoteDecoration: BoxDecoration(
          color: AppColors.royalIndigo.withOpacity(0.05),
          // borderLeft: BorderSide(
          //   color: AppColors.royalIndigo.withOpacity(0.3),
          //   width: 4,
          // ),
        ),
        listBullet: const TextStyle(
          color: AppColors.royalIndigo,
        ),
      ),
    );
  }

  /// 構建純文字內容
  Widget _buildPlainTextContent() {
    return SelectableText(
      _displayedContent,
      style: widget.textStyle ?? const TextStyle(
        fontSize: 16,
        height: 1.6,
        color: AppColors.textDark,
      ),
    );
  }

  /// 構建光標動畫
  Widget _buildCursor() {
    return AnimatedBuilder(
      animation: _cursorAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _cursorAnimation.value,
          child: Container(
            width: 2,
            height: 20,
            decoration: BoxDecoration(
              color: AppColors.royalIndigo,
              borderRadius: BorderRadius.circular(1),
            ),
          ),
        );
      },
    );
  }

  /// 獲取當前顯示的內容
  String get currentContent => _displayedContent;

  /// 是否已完成
  bool get isComplete => _isComplete;

  /// 是否有錯誤
  bool get hasError => _hasError;

  /// 錯誤消息
  String get errorMessage => _errorMessage;
}
