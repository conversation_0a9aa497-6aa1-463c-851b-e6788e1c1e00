import 'package:flutter/material.dart';

import '../../../data/models/astrology/chart_type.dart';
import '../../presentation/viewmodels/chart_viewmodel.dart';

class ChartTitleWidget extends StatelessWidget {
  final ChartViewModel viewModel;

  const ChartTitleWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth <= 600;

    return Container(
      margin: EdgeInsets.all(isMobile ? 4 : 8),
      padding: EdgeInsets.symmetric(
        horizontal: isMobile ? 8 : 12,
        vertical: isMobile ? 6 : 8,
      ),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 標題行
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _getChartTypeIcon(),
                color: Colors.blue.shade600,
                size: isMobile ? 14 : 16,
              ),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  _getChartTitle(),
                  style: TextStyle(
                    fontSize: isMobile ? 11 : 13,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade800,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          // 根據星盤類型顯示不同的信息
          ..._buildChartSpecificInfo(isMobile),
        ],
      ),
    );
  }

  /// 根據星盤類型構建特定信息
  List<Widget> _buildChartSpecificInfo(bool isMobile) {
    List<Widget> widgets = [];

    if (viewModel.chartType.isPredictiveChart || viewModel.chartType.isReturnChart) {
      // 推運盤或返照盤：顯示推運時間
      widgets.addAll(_buildTransitInfo(isMobile));
    }

    if (viewModel.chartType.requiresTwoPersons && viewModel.secondaryPerson != null) {
      // 合盤：顯示第二個人的信息
      widgets.addAll(_buildSecondaryPersonInfo(isMobile));
    } else {
      // 一般星盤：顯示主要人物信息
      widgets.addAll(_buildPrimaryPersonInfo(isMobile));
    }

    return widgets;
  }

  /// 構建推運時間信息
  List<Widget> _buildTransitInfo(bool isMobile) {
    // 對於返照盤，優先顯示 returnDate；對於其他類型，顯示 specificDate
    DateTime? displayDate;
    if (viewModel.chartType.isReturnChart && viewModel.chartData.returnDate != null) {
      displayDate = viewModel.chartData.returnDate;
    } else if (viewModel.specificDate != null) {
      displayDate = viewModel.specificDate;
    }

    if (displayDate == null) return [];

    // 根據星盤類型決定顯示的標籤
    String timeLabel;
    IconData timeIcon;
    Color iconColor;

    if (viewModel.chartType.name == '法達盤') {
      timeLabel = '法達時間：${_formatDateTime(displayDate)}';
      timeIcon = Icons.schedule;
      iconColor = Colors.orange.shade600;
    } else if (viewModel.chartType == ChartType.solarReturn) {
      timeLabel = '太陽返照：${_formatDateTime(displayDate)}';
      timeIcon = Icons.wb_sunny;
      iconColor = Colors.orange.shade600;
    } else if (viewModel.chartType == ChartType.lunarReturn) {
      timeLabel = '月亮返照：${_formatDateTime(displayDate)}';
      timeIcon = Icons.brightness_3;
      iconColor = Colors.blue.shade600;
    } else {
      timeLabel = '推運時間：${_formatDateTime(displayDate)}';
      timeIcon = Icons.timeline;
      iconColor = Colors.orange.shade600;
    }

    return [
      const SizedBox(height: 2),
      Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            timeIcon,
            size: isMobile ? 10 : 12,
            color: iconColor,
          ),
          const SizedBox(width: 3),
          Flexible(
            child: Text(
              timeLabel,
              style: TextStyle(
                fontSize: isMobile ? 9 : 10,
                color: iconColor,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    ];
  }

  /// 構建主要人物信息
  List<Widget> _buildPrimaryPersonInfo(bool isMobile) {
    return [
      const SizedBox(height: 2),
      Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.access_time,
            size: isMobile ? 10 : 12,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 3),
          Flexible(
            child: Text(
              _formatDateTime(viewModel.primaryPerson.dateTime),
              style: TextStyle(
                fontSize: isMobile ? 9 : 10,
                color: Colors.grey.shade600,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
      const SizedBox(height: 2),
      Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.location_on,
            size: isMobile ? 10 : 12,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 3),
          Flexible(
            child: Text(
              _formatPlace(viewModel.primaryPerson.birthPlace),
              style: TextStyle(
                fontSize: isMobile ? 9 : 10,
                color: Colors.grey.shade600,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    ];
  }

  /// 構建第二個人的信息
  List<Widget> _buildSecondaryPersonInfo(bool isMobile) {
    if (viewModel.secondaryPerson == null) return [];

    return [
      // 主要人物信息
      const SizedBox(height: 2),
      Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.person,
            size: isMobile ? 10 : 12,
            color: Colors.blue.shade600,
          ),
          const SizedBox(width: 3),
          Flexible(
            child: Text(
              '${viewModel.primaryPerson.name} ${_formatDateTime(viewModel.primaryPerson.dateTime)}',
              style: TextStyle(
                fontSize: isMobile ? 9 : 10,
                color: Colors.blue.shade600,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),

      // 次要人物信息
      const SizedBox(height: 2),
      Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.person_outline,
            size: isMobile ? 10 : 12,
            color: Colors.green.shade600,
          ),
          const SizedBox(width: 3),
          Flexible(
            child: Text(
              '${viewModel.secondaryPerson!.name} ${_formatDateTime(viewModel.secondaryPerson!.dateTime)}',
              style: TextStyle(
                fontSize: isMobile ? 9 : 10,
                color: Colors.green.shade600,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    ];
  }

  IconData _getChartTypeIcon() {
    switch (viewModel.chartType.name) {
      case '本命盤':
        return Icons.person;
      case '比較盤':
      case '合盤':
        return Icons.people;
      case '行運盤':
        return Icons.timeline;
      case '推運盤':
        return Icons.trending_up;
      case '返照盤':
        return Icons.refresh;
      case '法達盤':
        return Icons.schedule;
      default:
        return Icons.circle;
    }
  }

  String _getSimplifiedTitle() {
    final primaryName = viewModel.primaryPerson.name;
    final chartTypeName = viewModel.chartType.name;

    // 簡化標題，只顯示姓名和星盤類型
    if (primaryName.length > 8) {
      return '${primaryName.substring(0, 8)}... $chartTypeName';
    }
    return '$primaryName $chartTypeName';
  }

  String _getChartTitle() {
    final primaryName = viewModel.primaryPerson.name;
    final secondaryName = viewModel.secondaryPerson?.name;
    final chartTypeName = viewModel.chartType.name;

    // 簡化姓名顯示
    String simplifiedPrimaryName = primaryName.length > 6
        ? '${primaryName.substring(0, 6)}...'
        : primaryName;

    String? simplifiedSecondaryName;
    if (secondaryName != null) {
      simplifiedSecondaryName = secondaryName.length > 6
          ? '${secondaryName.substring(0, 6)}...'
          : secondaryName;
    }

    switch (viewModel.chartType.name) {
      case '世俗盤':
      case '分至盤':
        return chartTypeName;
      case '本命盤':
        return '$simplifiedPrimaryName $chartTypeName';
      default:
        if (viewModel.chartType.requiresTwoPersons && viewModel.secondaryPerson != null) {
          return '$simplifiedPrimaryName & $simplifiedSecondaryName $chartTypeName';
        } else {
          return '$simplifiedPrimaryName $chartTypeName';
        }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatPlace(String place) {
    // 地點超過12個字符時截斷
    if (place.length > 12) {
      return '${place.substring(0, 12)}...';
    }
    return place;
  }
}
