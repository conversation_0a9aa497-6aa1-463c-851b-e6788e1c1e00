import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../astreal.dart';
import '../../presentation/pages/video_list_page.dart';

/// 占星分析注意事項對話框
class AstrologyNoticeDialog extends StatefulWidget {
  /// 是否顯示模式切換選項
  final bool showModeToggle;

  /// 是否顯示分析方式切換選項
  final bool showAnalysisMethodToggle;

  /// 是否顯示影片教學和評價區塊
  final bool showVideoAndReviews;

  /// 自訂標題
  final String? customTitle;

  /// 自訂注意事項內容
  final List<String>? customNotices;

  const AstrologyNoticeDialog({
    super.key,
    this.showModeToggle = true,
    this.showAnalysisMethodToggle = true,
    this.showVideoAndReviews = true,
    this.customTitle,
    this.customNotices,
  });

  @override
  State<AstrologyNoticeDialog> createState() => _AstrologyNoticeDialogState();

  /// 顯示對話框的靜態方法
  static Future<bool?> show(
    BuildContext context, {
    bool showModeToggle = true,
    bool showAnalysisMethodToggle = true,
    bool showVideoAndReviews = true,
    String? customTitle,
    List<String>? customNotices,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AstrologyNoticeDialog(
        showModeToggle: showModeToggle,
        showAnalysisMethodToggle: showAnalysisMethodToggle,
        showVideoAndReviews: showVideoAndReviews,
        customTitle: customTitle,
        customNotices: customNotices,
      ),
    );
  }
}

class _AstrologyNoticeDialogState extends State<AstrologyNoticeDialog> {
  String _userMode = 'starmaster'; // 'starlight' 或 'starmaster'
  String _analysisMethod = 'modern'; // 'classical' 或 'modern'
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// 載入設置
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _userMode = prefs.getString('user_mode') ?? 'starmaster';
        _analysisMethod = prefs.getString('analysis_method') ?? 'modern';
        _isLoading = false;
      });
    } catch (e) {
      logger.e('載入設置失敗: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Dialog(
        child: SizedBox(
          width: 100,
          height: 100,
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }
    final mediaQuery = MediaQuery.of(context).size;
    final padding = 24.0; // 可以依需求改成 16.0 或更小
    final dialogWidth = mediaQuery.width - padding * 2;
    final dialogHeight = mediaQuery.height - padding * 2;

    return Dialog(
        backgroundColor: Colors.transparent,
        // 移除預設白底，讓我們的容器掌控外觀
        insetPadding: EdgeInsets.zero,
        // 移除 Dialog 的預設邊距
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        clipBehavior: Clip.antiAlias,
        // 確保圓弧正確顯示
        child: Center(
          child: Container(
            width: dialogWidth,
            height: dialogHeight,
            constraints: BoxConstraints(
              maxWidth: 500,
              maxHeight: MediaQuery.of(context).size.height * 0.95,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16), // 確保容器也有圓弧
            ),
            child: Column(
              children: [
                // 固定標題區域
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.amber.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.info_outline,
                          color: Colors.amber,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          widget.customTitle ?? '占星分析注意事項',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // 可滾動內容區域
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 注意事項內容
                        _buildNoticeContent(),
                        const SizedBox(height: 16),

                        // 模式切換按鈕
                        if (widget.showModeToggle) ...[
                          _buildModeToggleButton(),
                          const SizedBox(height: 16),
                        ],

                        // 分析方式切換按鈕
                        if (widget.showAnalysisMethodToggle) ...[
                          _buildAnalysisMethodToggleButton(),
                        ],

                        // YouTube 範例影片和評價區塊
                        if (widget.showVideoAndReviews) ...[
                          const SizedBox(height: 16),
                          _buildVideoAndReviewsSection(),
                        ],
                      ],
                    ),
                  ),
                ),

                // 固定按鈕區域
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(16),
                      bottomRight: Radius.circular(16),
                    ),
                    border: Border(
                      top: BorderSide(
                        color: Colors.grey.withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            side: const BorderSide(color: Colors.grey),
                          ),
                          child: const Text(
                            '取消',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => Navigator.of(context).pop(true),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.royalIndigo,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: const Text('我了解，繼續'),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  /// 構建模式切換按鈕
  Widget _buildModeToggleButton() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.swap_horiz,
                color: AppColors.royalIndigo,
                size: 20,
              ),
              const SizedBox(width: 4),
              Text(
                '解讀模式',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () => _showModeExplanationDialog(),
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.help_outline,
                    color: AppColors.royalIndigo,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildModeButton(
                  mode: 'starlight',
                  title: '初心者模式',
                  subtitle: '簡化解讀，易於理解',
                  icon: Icons.star_outline,
                  isSelected: _userMode == 'starlight',
                  onTap: () {
                    setState(() {
                      _userMode = 'starlight';
                    });
                    _saveUserMode('starlight');
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildModeButton(
                  mode: 'starmaster',
                  title: '占星師模式',
                  subtitle: '專業解讀，深度分析',
                  icon: Icons.star,
                  isSelected: _userMode == 'starmaster',
                  onTap: () {
                    setState(() {
                      _userMode = 'starmaster';
                    });
                    _saveUserMode('starmaster');
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建分析方式切換按鈕
  Widget _buildAnalysisMethodToggleButton() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.purple.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: Colors.purple[700],
                size: 20,
              ),
              const SizedBox(width: 4),
              Text(
                '分析方式',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.purple[700],
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () => _showAnalysisMethodExplanationDialog(),
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.purple.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.help_outline,
                    color: Colors.purple[700],
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildAnalysisMethodButton(
                  method: 'modern',
                  title: '現代占星',
                  subtitle: '普拉西德制，現代技法',
                  icon: Icons.science,
                  isSelected: _analysisMethod == 'modern',
                  onTap: () {
                    setState(() {
                      _analysisMethod = 'modern';
                    });
                    _saveAnalysisMethod('modern');
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildAnalysisMethodButton(
                  method: 'classical',
                  title: '古典占星',
                  subtitle: '整宮制，傳統技法',
                  icon: Icons.account_balance,
                  isSelected: _analysisMethod == 'classical',
                  onTap: () {
                    setState(() {
                      _analysisMethod = 'classical';
                    });
                    _saveAnalysisMethod('classical');
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建模式按鈕
  Widget _buildModeButton({
    required String mode,
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.royalIndigo.withValues(alpha: 0.1)
              : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? AppColors.royalIndigo
                : Colors.grey.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? AppColors.royalIndigo : Colors.grey[600],
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: isSelected ? AppColors.royalIndigo : Colors.grey[700],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 構建分析方式按鈕
  Widget _buildAnalysisMethodButton({
    required String method,
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color:
              isSelected ? Colors.purple.withValues(alpha: 0.1) : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? Colors.purple[700]!
                : Colors.grey.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.purple[700] : Colors.grey[600],
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: isSelected ? Colors.purple[700] : Colors.grey[700],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 構建注意事項內容
  Widget _buildNoticeContent() {
    final notices = widget.customNotices ??
        [
          '占星分析僅供參考，不應作為人生重大決定的唯一依據',
          '每個人都有自由意志，可以選擇自己的人生道路',
          '建議將占星作為自我了解和成長的工具之一',
          '如有心理健康問題，請尋求專業心理諮詢師協助',
        ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: notices
            .map((notice) => Padding(
                  padding: const EdgeInsets.only(bottom: 0),
                  child: Text(
                    '• $notice',
                    style: const TextStyle(fontSize: 14, height: 1.5),
                  ),
                ))
            .toList(),
      ),
    );
  }

  /// 構建影片和評價區塊
  Widget _buildVideoAndReviewsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '📺 想了解更多？',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          // 影片教學按鈕
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _navigateToVideoTutorials(),
              icon: const Icon(Icons.video_library, color: Colors.blue),
              label: const Text(
                '觀看影片教學',
                style: TextStyle(fontSize: 14, color: Colors.black87),
              ),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 8),
                side: BorderSide(color: Colors.blue.withValues(alpha: 0.3)),
              ),
            ),
          ),
          const SizedBox(height: 4),

          // 查看評價按鈕
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _showUserReviews(),
              icon: const Icon(Icons.star_outline, color: Colors.orange),
              label: const Text(
                '查看用戶評價與心得',
                style: TextStyle(fontSize: 14, color: Colors.black87),
              ),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 8),
                side: BorderSide(color: Colors.orange.withValues(alpha: 0.3)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 保存用戶模式設置
  Future<void> _saveUserMode(String mode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_mode', mode);

      // 顯示模式切換提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              mode == 'starlight' ? '已切換至初心者模式，解讀將更簡化易懂' : '已切換至占星師模式，解讀將更專業深入',
            ),
            backgroundColor: AppColors.royalIndigo,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      logger.e('保存用戶模式失敗: $e');
    }
  }

  /// 保存分析方式設置
  Future<void> _saveAnalysisMethod(String method) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('analysis_method', method);

      // 顯示分析方式切換提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              method == 'classical'
                  ? '已切換至古典占星，將使用整宮制和傳統技法'
                  : '已切換至現代占星，將使用普拉西德制和現代技法',
            ),
            backgroundColor: Colors.purple[700],
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      logger.e('保存分析方式失敗: $e');
    }
  }

  /// 導航到影片教學頁面
  void _navigateToVideoTutorials() {
    try {
      logger.d('導航到影片教學頁面');
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const VideoListPage(),
        ),
      );
    } catch (e) {
      logger.e('導航到影片教學頁面失敗: $e');
      _showErrorSnackBar('開啟影片教學時發生錯誤');
    }
  }

  /// 顯示錯誤訊息
  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  /// 顯示用戶評價
  Future<void> _showUserReviews() async {
    try {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Container(
              constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 標題
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        color: Colors.orange,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        '用戶評價與心得',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // 評價列表
                  Expanded(
                    child: FutureBuilder<List<Map<String, dynamic>>>(
                      future: _loadUserReviews(),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState ==
                            ConnectionState.waiting) {
                          return const Center(
                              child: CircularProgressIndicator());
                        }

                        if (snapshot.hasError ||
                            !snapshot.hasData ||
                            snapshot.data!.isEmpty) {
                          return _buildDefaultReviews();
                        }

                        return ListView.separated(
                          itemCount: snapshot.data!.length,
                          separatorBuilder: (context, index) => const Divider(),
                          itemBuilder: (context, index) {
                            final review = snapshot.data![index];
                            return _buildReviewItem(review);
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    } catch (e) {
      logger.e('顯示用戶評價失敗 $e');
      _showErrorSnackBar('載入評價時發生錯誤');
    }
  }

  /// 載入用戶評價
  Future<List<Map<String, dynamic>>> _loadUserReviews() async {
    try {
      final firestore = FirebaseFirestore.instance;

      // 從 analysis_ratings 集合載入公開評價（統一評價系統）
      final ratingsSnapshot = await firestore
          .collection('analysis_ratings')
          .where('isPublic', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(10)
          .get();

      final reviews = ratingsSnapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'userName': data['userName'] ?? '匿名用戶',
          'rating': data['rating'] ?? 5,
          'comment': data['comment'] ?? '',
          'createdAt': data['createdAt'] as Timestamp?,
          'analysisType': data['chartType'] ?? '',
        };
      }).toList();

      return reviews;
    } catch (e) {
      logger.e('載入用戶評價失敗 $e');
      return [];
    }
  }

  /// 構建預設評價（當沒有真實評價時顯示）
  Widget _buildDefaultReviews() {
    final defaultReviews = [
      {
        'userName': '星座愛好者',
        'rating': 5,
        'comment': '分析很準確，幫助我更了解自己的性格特質和潛在能力，對人生規劃很有幫助！',
        'createdAt': null,
        'analysisType': '本命盤分析',
      },
      {
        'userName': '占星初學者',
        'rating': 4,
        'comment': '內容很詳細，專業術語解釋得很清楚，讓我這個新手也能看懂，很棒的學習工具。',
        'createdAt': null,
        'analysisType': '流年分析',
      },
      {
        'userName': '心理學愛好者',
        'rating': 5,
        'comment': '從占星角度分析性格很有趣，與心理學有很多共通點，給了我新的自我認知視角。',
        'createdAt': null,
        'analysisType': '性格分析',
      },
      {
        'userName': '職場新人',
        'rating': 4,
        'comment': '職業分析部分對我的職涯規劃很有啟發，雖然不能完全依賴，但確實提供了思考方向。',
        'createdAt': null,
        'analysisType': '職業分析',
      },
      {
        'userName': '感情困惑者',
        'rating': 5,
        'comment': '感情分析讓我更了解自己在關係中的模式，對改善人際關係很有幫助，推薦！',
        'createdAt': null,
        'analysisType': '感情分析',
      },
    ];

    return ListView.separated(
      itemCount: defaultReviews.length,
      separatorBuilder: (context, index) => const Divider(),
      itemBuilder: (context, index) {
        return _buildReviewItem(defaultReviews[index]);
      },
    );
  }

  /// 構建評價項目
  Widget _buildReviewItem(Map<String, dynamic> review) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用戶信息和評分
          Row(
            children: [
              CircleAvatar(
                radius: 18,
                backgroundColor: Colors.blue.withValues(alpha: 0.1),
                child: Text(
                  review['userName'][0].toUpperCase(),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          review['userName'],
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: AppColors.textDark,
                          ),
                        ),
                        if (review['analysisType'] != null &&
                            review['analysisType'].toString().isNotEmpty) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.blue.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              review['analysisType'],
                              style: const TextStyle(
                                fontSize: 10,
                                color: Colors.blue,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 2),
                    Row(
                      children: [
                        ...List.generate(5, (index) {
                          return Icon(
                            index < review['rating']
                                ? Icons.star
                                : Icons.star_border,
                            color: Colors.amber,
                            size: 14,
                          );
                        }),
                        const SizedBox(width: 4),
                        Text(
                          '${review['rating']}/5',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),

          // 評價內容
          Text(
            review['comment'],
            style: const TextStyle(
              fontSize: 13,
              color: Colors.black87,
              height: 1.5,
            ),
          ),

          // 日期
          if (review['createdAt'] != null) ...[
            const SizedBox(height: 6),
            Text(
              _formatDate(review['createdAt']),
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey[500],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 格式化日期
  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return '';
    final date = timestamp.toDate();
    return '${date.year}/${date.month}/${date.day}';
  }

  /// 顯示模式解釋對話框
  void _showModeExplanationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              Icons.swap_horiz,
              color: AppColors.royalIndigo,
              size: 24,
            ),
            const SizedBox(width: 8),
            const Text(
              '解讀模式說明',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildModeExplanationItem(
                icon: Icons.star_outline,
                title: '初心者模式 (Starlight)',
                subtitle: '適合占星初學者',
                features: [
                  '使用簡化的占星術語',
                  '重點解釋基本概念',
                  '提供通俗易懂的分析',
                  '避免過於複雜的技術細節',
                  '適合剛接觸占星的用戶',
                ],
                color: AppColors.royalIndigo,
              ),
              const SizedBox(height: 16),
              _buildModeExplanationItem(
                icon: Icons.star,
                title: '占星師模式 (Starmaster)',
                subtitle: '適合專業占星師',
                features: [
                  '使用專業的占星術語',
                  '提供深度的技術分析',
                  '包含複雜的占星技法',
                  '詳細的相位和宮位解釋',
                  '適合有經驗的占星師',
                ],
                color: AppColors.royalIndigo,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '💡 提示：您可以隨時在設置中切換模式，或在每次分析前選擇適合的模式。',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.blue,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('我了解了'),
          ),
        ],
      ),
    );
  }

  /// 顯示分析方式解釋對話框
  void _showAnalysisMethodExplanationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              Icons.analytics,
              color: Colors.purple[700],
              size: 24,
            ),
            const SizedBox(width: 8),
            const Text(
              '分析方式說明',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildAnalysisMethodExplanationItem(
                icon: Icons.account_balance,
                title: '古典占星 (Classical)',
                subtitle: '傳統占星學派',
                features: [
                  '使用整宮制 (Whole Sign Houses)',
                  '重視傳統七星 (太陽到土星)',
                  '採用古典占星技法',
                  '注重事件預測和時間選擇',
                  '基於希臘-阿拉伯占星傳統',
                ],
                color: Colors.purple[700]!,
              ),
              const SizedBox(height: 16),
              _buildAnalysisMethodExplanationItem(
                icon: Icons.science,
                title: '現代占星 (Modern)',
                subtitle: '現代心理占星學派',
                features: [
                  '使用普拉西德制 (Placidus Houses)',
                  '包含現代行星 (天王星、海王星、冥王星)',
                  '融合心理學理論',
                  '重視個人成長和心理分析',
                  '基於20世紀以來的占星發展',
                ],
                color: Colors.purple[700]!,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '⚖️ 選擇建議：古典占星適合傳統預測和事件分析，現代占星適合心理分析和個人成長。您可以根據分析目的選擇合適的方式。',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.orange,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('我了解了'),
          ),
        ],
      ),
    );
  }

  /// 構建模式解釋項目
  Widget _buildModeExplanationItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required List<String> features,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...features.map((feature) => Padding(
                padding: const EdgeInsets.only(bottom: 6),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(top: 6),
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        feature,
                        style: const TextStyle(fontSize: 14, height: 1.4),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  /// 構建分析方式解釋項目
  Widget _buildAnalysisMethodExplanationItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required List<String> features,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...features.map((feature) => Padding(
                padding: const EdgeInsets.only(bottom: 6),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(top: 6),
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        feature,
                        style: const TextStyle(fontSize: 14, height: 1.4),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }
}
