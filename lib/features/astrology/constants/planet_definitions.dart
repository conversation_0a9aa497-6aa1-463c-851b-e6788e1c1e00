import 'package:flutter/material.dart';
import 'package:sweph/sweph.dart';

/// 行星定義常數
/// 
/// 包含所有行星的詳細定義和屬性
class PlanetDefinitions {
  /// 傳統七星
  static const List<Map<String, dynamic>> traditionalPlanets = [
    {
      'id': 0,
      'name': '太陽',
      'englishName': 'Sun',
      'symbol': '☉',
      'unicode': '\u2609',
      'body': HeavenlyBody.SE_SUN,
      'color': Color(0xFFFFD700),
      'isLuminary': true,
      'isPersonal': true,
      'isVisible': true,
      'orbitalPeriod': 365.25, // 天
      'averageSpeed': 0.9856, // 度/天
      'keywords': ['自我', '意志', '生命力', '創造力', '權威', '父親'],
      'rulership': ['獅子座'],
      'exaltation': ['白羊座'],
      'detriment': ['水瓶座'],
      'fall': ['天秤座'],
      'element': '火',
      'nature': '陽性',
      'description': '代表自我意識、生命力和創造力的核心',
    },
    {
      'id': 1,
      'name': '月亮',
      'englishName': 'Moon',
      'symbol': '☽',
      'unicode': '\u263D',
      'body': HeavenlyBody.SE_MOON,
      'color': Color(0xFFC0C0C0),
      'isLuminary': true,
      'isPersonal': true,
      'isVisible': true,
      'orbitalPeriod': 27.32, // 天
      'averageSpeed': 13.176, // 度/天
      'keywords': ['情感', '直覺', '習慣', '母性', '記憶', '潛意識'],
      'rulership': ['巨蟹座'],
      'exaltation': ['金牛座'],
      'detriment': ['摩羯座'],
      'fall': ['天蠍座'],
      'element': '水',
      'nature': '陰性',
      'description': '代表情感、直覺和潛意識的反應模式',
    },
    {
      'id': 2,
      'name': '水星',
      'englishName': 'Mercury',
      'symbol': '☿',
      'unicode': '\u263F',
      'body': HeavenlyBody.SE_MERCURY,
      'color': Color(0xFF87CEEB),
      'isLuminary': false,
      'isPersonal': true,
      'isVisible': true,
      'orbitalPeriod': 87.97, // 天
      'averageSpeed': 4.092, // 度/天
      'keywords': ['溝通', '思維', '學習', '交通', '商業', '技能'],
      'rulership': ['雙子座', '處女座'],
      'exaltation': ['處女座'],
      'detriment': ['射手座', '雙魚座'],
      'fall': ['雙魚座'],
      'element': '風',
      'nature': '中性',
      'description': '代表溝通、思維和學習能力',
    },
    {
      'id': 3,
      'name': '金星',
      'englishName': 'Venus',
      'symbol': '♀',
      'unicode': '\u2640',
      'body': HeavenlyBody.SE_VENUS,
      'color': Color(0xFFFF69B4),
      'isLuminary': false,
      'isPersonal': true,
      'isVisible': true,
      'orbitalPeriod': 224.7, // 天
      'averageSpeed': 1.602, // 度/天
      'keywords': ['愛情', '美感', '價值', '和諧', '藝術', '關係'],
      'rulership': ['金牛座', '天秤座'],
      'exaltation': ['雙魚座'],
      'detriment': ['天蠍座', '白羊座'],
      'fall': ['處女座'],
      'element': '土',
      'nature': '陰性',
      'description': '代表愛情、美感和價值觀',
    },
    {
      'id': 4,
      'name': '火星',
      'englishName': 'Mars',
      'symbol': '♂',
      'unicode': '\u2642',
      'body': HeavenlyBody.SE_MARS,
      'color': Color(0xFFFF4500),
      'isLuminary': false,
      'isPersonal': true,
      'isVisible': true,
      'orbitalPeriod': 686.98, // 天
      'averageSpeed': 0.524, // 度/天
      'keywords': ['行動', '慾望', '競爭', '勇氣', '戰鬥', '性'],
      'rulership': ['白羊座', '天蠍座'],
      'exaltation': ['摩羯座'],
      'detriment': ['天秤座', '金牛座'],
      'fall': ['巨蟹座'],
      'element': '火',
      'nature': '陽性',
      'description': '代表行動力、慾望和競爭精神',
    },
    {
      'id': 5,
      'name': '木星',
      'englishName': 'Jupiter',
      'symbol': '♃',
      'unicode': '\u2643',
      'body': HeavenlyBody.SE_JUPITER,
      'color': Color(0xFF9370DB),
      'isLuminary': false,
      'isPersonal': false,
      'isVisible': true,
      'orbitalPeriod': 4332.59, // 天
      'averageSpeed': 0.083, // 度/天
      'keywords': ['擴展', '智慧', '幸運', '哲學', '宗教', '成長'],
      'rulership': ['射手座', '雙魚座'],
      'exaltation': ['巨蟹座'],
      'detriment': ['雙子座', '處女座'],
      'fall': ['摩羯座'],
      'element': '火',
      'nature': '陽性',
      'description': '代表擴展、智慧和機會',
    },
    {
      'id': 6,
      'name': '土星',
      'englishName': 'Saturn',
      'symbol': '♄',
      'unicode': '\u2644',
      'body': HeavenlyBody.SE_SATURN,
      'color': Color(0xFF2F4F4F),
      'isLuminary': false,
      'isPersonal': false,
      'isVisible': true,
      'orbitalPeriod': 10759.22, // 天
      'averageSpeed': 0.033, // 度/天
      'keywords': ['限制', '責任', '結構', '耐心', '紀律', '時間'],
      'rulership': ['摩羯座', '水瓶座'],
      'exaltation': ['天秤座'],
      'detriment': ['巨蟹座', '獅子座'],
      'fall': ['白羊座'],
      'element': '土',
      'nature': '陰性',
      'description': '代表限制、責任和結構化',
    },
  ];
  
  /// 現代行星
  static const List<Map<String, dynamic>> modernPlanets = [
    {
      'id': 7,
      'name': '天王星',
      'englishName': 'Uranus',
      'symbol': '♅',
      'unicode': '\u2645',
      'body': HeavenlyBody.SE_URANUS,
      'color': Color(0xFF00CED1),
      'isLuminary': false,
      'isPersonal': false,
      'isVisible': true,
      'orbitalPeriod': 30688.5, // 天
      'averageSpeed': 0.012, // 度/天
      'keywords': ['革新', '獨立', '突變', '科技', '自由', '反叛'],
      'rulership': ['水瓶座'],
      'exaltation': ['天蠍座'],
      'detriment': ['獅子座'],
      'fall': ['金牛座'],
      'element': '風',
      'nature': '陽性',
      'description': '代表革新、獨立和突破傳統',
    },
    {
      'id': 8,
      'name': '海王星',
      'englishName': 'Neptune',
      'symbol': '♆',
      'unicode': '\u2646',
      'body': HeavenlyBody.SE_NEPTUNE,
      'color': Color(0xFF4169E1),
      'isLuminary': false,
      'isPersonal': false,
      'isVisible': true,
      'orbitalPeriod': 60182, // 天
      'averageSpeed': 0.006, // 度/天
      'keywords': ['夢想', '靈性', '迷惑', '同情', '藝術', '犧牲'],
      'rulership': ['雙魚座'],
      'exaltation': ['獅子座'],
      'detriment': ['處女座'],
      'fall': ['水瓶座'],
      'element': '水',
      'nature': '陰性',
      'description': '代表夢想、靈性和超越現實',
    },
    {
      'id': 9,
      'name': '冥王星',
      'englishName': 'Pluto',
      'symbol': '♇',
      'unicode': '\u2647',
      'body': HeavenlyBody.SE_PLUTO,
      'color': Color(0xFF8B0000),
      'isLuminary': false,
      'isPersonal': false,
      'isVisible': true,
      'orbitalPeriod': 90560, // 天
      'averageSpeed': 0.004, // 度/天
      'keywords': ['轉化', '重生', '權力', '深度', '死亡', '再生'],
      'rulership': ['天蠍座'],
      'exaltation': ['水瓶座'],
      'detriment': ['金牛座'],
      'fall': ['獅子座'],
      'element': '水',
      'nature': '陰性',
      'description': '代表轉化、重生和深層變革',
    },
  ];
  
  /// 月亮交點
  static const List<Map<String, dynamic>> lunarNodes = [
    {
      'id': 10,
      'name': '北交點',
      'englishName': 'North Node',
      'symbol': '☊',
      'unicode': '\u260A',
      'body': HeavenlyBody.SE_TRUE_NODE,
      'color': Color(0xFF228B22),
      'isLuminary': false,
      'isPersonal': false,
      'isVisible': false,
      'orbitalPeriod': 6798.38, // 天（逆行）
      'averageSpeed': -0.053, // 度/天
      'keywords': ['成長', '目標', '未來', '發展', '靈魂使命'],
      'description': '代表靈魂的成長方向和今生的使命',
    },
    {
      'id': 11,
      'name': '南交點',
      'englishName': 'South Node',
      'symbol': '☋',
      'unicode': '\u260B',
      'color': Color(0xFFB22222),
      'isLuminary': false,
      'isPersonal': false,
      'isVisible': false,
      'orbitalPeriod': 6798.38, // 天（逆行）
      'averageSpeed': -0.053, // 度/天
      'keywords': ['過去', '天賦', '習慣', '釋放', '前世'],
      'description': '代表過去的經驗和需要釋放的模式',
    },
  ];
  
  /// 小行星
  static const List<Map<String, dynamic>> asteroids = [
    {
      'id': 12,
      'name': '凱龍星',
      'englishName': 'Chiron',
      'symbol': '⚷',
      'unicode': '\u26B7',
      'body': HeavenlyBody.SE_CHIRON,
      'color': Color(0xFF800080),
      'isLuminary': false,
      'isPersonal': false,
      'isVisible': false,
      'orbitalPeriod': 18396, // 天
      'averageSpeed': 0.02, // 度/天
      'keywords': ['療癒', '傷痛', '智慧', '教導', '導師'],
      'description': '代表療癒師和受傷的治療者',
    },
    {
      'id': 13,
      'name': '穀神星',
      'englishName': 'Ceres',
      'symbol': '⚳',
      'unicode': '\u26B3',
      'body': HeavenlyBody.SE_CERES,
      'color': Color(0xFF8FBC8F),
      'keywords': ['滋養', '母性', '農業', '照顧'],
      'description': '代表滋養和照顧他人的能力',
    },
    {
      'id': 14,
      'name': '智神星',
      'englishName': 'Pallas',
      'symbol': '⚴',
      'unicode': '\u26B4',
      'body': HeavenlyBody.SE_PALLAS,
      'color': Color(0xFF4682B4),
      'keywords': ['智慧', '策略', '藝術', '正義'],
      'description': '代表智慧、策略和創造性解決問題',
    },
    {
      'id': 15,
      'name': '婚神星',
      'englishName': 'Juno',
      'symbol': '⚵',
      'unicode': '\u26B5',
      'body': HeavenlyBody.SE_JUNO,
      'color': Color(0xFFDDA0DD),
      'keywords': ['婚姻', '承諾', '伴侶', '忠誠'],
      'description': '代表婚姻和長期承諾關係',
    },
    {
      'id': 16,
      'name': '灶神星',
      'englishName': 'Vesta',
      'symbol': '⚶',
      'unicode': '\u26B6',
      'body': HeavenlyBody.SE_VESTA,
      'color': Color(0xFFFF6347),
      'keywords': ['奉獻', '專注', '純潔', '服務'],
      'description': '代表奉獻、專注和神聖的服務',
    },
  ];
  
  /// 虛點
  static const List<Map<String, dynamic>> fictionalPoints = [
    {
      'id': 17,
      'name': '莉莉絲',
      'englishName': 'Lilith',
      'symbol': '⚸',
      'unicode': '\u26B8',
      'body': HeavenlyBody.SE_MEAN_APOG,
      'color': Color(0xFF8B0000),
      'keywords': ['陰暗面', '原始', '獨立', '反叛'],
      'description': '代表原始的女性力量和被壓抑的慾望',
    },
  ];
  
  /// 所有行星
  static List<Map<String, dynamic>> get allPlanets => [
        ...traditionalPlanets,
        ...modernPlanets,
        ...lunarNodes,
        ...asteroids,
        ...fictionalPoints,
      ];
  
  /// 根據名稱獲取行星定義
  static Map<String, dynamic>? getPlanetByName(String name) {
    try {
      return allPlanets.firstWhere((planet) => planet['name'] == name);
    } catch (e) {
      return null;
    }
  }
  
  /// 根據 ID 獲取行星定義
  static Map<String, dynamic>? getPlanetById(int id) {
    try {
      return allPlanets.firstWhere((planet) => planet['id'] == id);
    } catch (e) {
      return null;
    }
  }
  
  /// 根據 HeavenlyBody 獲取行星定義
  static Map<String, dynamic>? getPlanetByBody(HeavenlyBody body) {
    try {
      return allPlanets.firstWhere((planet) => planet['body'] == body);
    } catch (e) {
      return null;
    }
  }
  
  /// 獲取個人行星列表
  static List<Map<String, dynamic>> getPersonalPlanets() {
    return allPlanets.where((planet) => planet['isPersonal'] == true).toList();
  }
  
  /// 獲取社會行星列表
  static List<Map<String, dynamic>> getSocialPlanets() {
    return [
      getPlanetByName('木星')!,
      getPlanetByName('土星')!,
    ];
  }
  
  /// 獲取外行星列表
  static List<Map<String, dynamic>> getOuterPlanets() {
    return [
      getPlanetByName('天王星')!,
      getPlanetByName('海王星')!,
      getPlanetByName('冥王星')!,
    ];
  }
  
  /// 獲取發光體列表
  static List<Map<String, dynamic>> getLuminaries() {
    return allPlanets.where((planet) => planet['isLuminary'] == true).toList();
  }
  
  /// 獲取可見行星列表
  static List<Map<String, dynamic>> getVisiblePlanets() {
    return allPlanets.where((planet) => planet['isVisible'] == true).toList();
  }
}
