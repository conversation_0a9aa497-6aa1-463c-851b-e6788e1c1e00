import 'package:flutter/material.dart';

import 'angle_calculator.dart';

/// 星座計算工具類
/// 
/// 提供星座相關的計算功能，包括：
/// - 根據黃經度數確定星座
/// - 星座屬性查詢
/// - 星座間的關係計算
class ZodiacCalculator {
  /// 12 星座的基本信息
  static const List<Map<String, dynamic>> zodiacSigns = [
    {
      'id': 1,
      'name': '白羊座',
      'englishName': 'Aries',
      'symbol': '♈',
      'element': '火',
      'quality': '基本',
      'ruler': '火星',
      'exaltation': '太陽',
      'detriment': '金星',
      'fall': '土星',
      'startDegree': 0.0,
      'endDegree': 30.0,
      'color': Colors.red,
      'keywords': ['主動', '衝動', '領導', '開創'],
    },
    {
      'id': 2,
      'name': '金牛座',
      'englishName': 'Taurus',
      'symbol': '♉',
      'element': '土',
      'quality': '固定',
      'ruler': '金星',
      'exaltation': '月亮',
      'detriment': '火星',
      'fall': '天王星',
      'startDegree': 30.0,
      'endDegree': 60.0,
      'color': Colors.green,
      'keywords': ['穩定', '實際', '感官', '持久'],
    },
    {
      'id': 3,
      'name': '雙子座',
      'englishName': 'Gemini',
      'symbol': '♊',
      'element': '風',
      'quality': '變動',
      'ruler': '水星',
      'exaltation': '北交點',
      'detriment': '木星',
      'fall': '南交點',
      'startDegree': 60.0,
      'endDegree': 90.0,
      'color': Colors.yellow,
      'keywords': ['溝通', '多變', '好奇', '靈活'],
    },
    {
      'id': 4,
      'name': '巨蟹座',
      'englishName': 'Cancer',
      'symbol': '♋',
      'element': '水',
      'quality': '基本',
      'ruler': '月亮',
      'exaltation': '木星',
      'detriment': '土星',
      'fall': '火星',
      'startDegree': 90.0,
      'endDegree': 120.0,
      'color': Colors.blue,
      'keywords': ['情感', '保護', '家庭', '直覺'],
    },
    {
      'id': 5,
      'name': '獅子座',
      'englishName': 'Leo',
      'symbol': '♌',
      'element': '火',
      'quality': '固定',
      'ruler': '太陽',
      'exaltation': '海王星',
      'detriment': '天王星',
      'fall': '水星',
      'startDegree': 120.0,
      'endDegree': 150.0,
      'color': Colors.orange,
      'keywords': ['創造', '表現', '自信', '慷慨'],
    },
    {
      'id': 6,
      'name': '處女座',
      'englishName': 'Virgo',
      'symbol': '♍',
      'element': '土',
      'quality': '變動',
      'ruler': '水星',
      'exaltation': '水星',
      'detriment': '木星',
      'fall': '金星',
      'startDegree': 150.0,
      'endDegree': 180.0,
      'color': Colors.brown,
      'keywords': ['分析', '完美', '服務', '實用'],
    },
    {
      'id': 7,
      'name': '天秤座',
      'englishName': 'Libra',
      'symbol': '♎',
      'element': '風',
      'quality': '基本',
      'ruler': '金星',
      'exaltation': '土星',
      'detriment': '火星',
      'fall': '太陽',
      'startDegree': 180.0,
      'endDegree': 210.0,
      'color': Colors.pink,
      'keywords': ['平衡', '和諧', '美感', '合作'],
    },
    {
      'id': 8,
      'name': '天蠍座',
      'englishName': 'Scorpio',
      'symbol': '♏',
      'element': '水',
      'quality': '固定',
      'ruler': '冥王星',
      'exaltation': '天王星',
      'detriment': '金星',
      'fall': '月亮',
      'startDegree': 210.0,
      'endDegree': 240.0,
      'color': Colors.deepPurple,
      'keywords': ['轉化', '深度', '神秘', '強烈'],
    },
    {
      'id': 9,
      'name': '射手座',
      'englishName': 'Sagittarius',
      'symbol': '♐',
      'element': '火',
      'quality': '變動',
      'ruler': '木星',
      'exaltation': '南交點',
      'detriment': '水星',
      'fall': '北交點',
      'startDegree': 240.0,
      'endDegree': 270.0,
      'color': Colors.purple,
      'keywords': ['探索', '哲學', '自由', '樂觀'],
    },
    {
      'id': 10,
      'name': '摩羯座',
      'englishName': 'Capricorn',
      'symbol': '♑',
      'element': '土',
      'quality': '基本',
      'ruler': '土星',
      'exaltation': '火星',
      'detriment': '月亮',
      'fall': '木星',
      'startDegree': 270.0,
      'endDegree': 300.0,
      'color': Colors.grey,
      'keywords': ['責任', '成就', '結構', '耐心'],
    },
    {
      'id': 11,
      'name': '水瓶座',
      'englishName': 'Aquarius',
      'symbol': '♒',
      'element': '風',
      'quality': '固定',
      'ruler': '天王星',
      'exaltation': '冥王星',
      'detriment': '太陽',
      'fall': '海王星',
      'startDegree': 300.0,
      'endDegree': 330.0,
      'color': Colors.cyan,
      'keywords': ['創新', '獨立', '人道', '未來'],
    },
    {
      'id': 12,
      'name': '雙魚座',
      'englishName': 'Pisces',
      'symbol': '♓',
      'element': '水',
      'quality': '變動',
      'ruler': '海王星',
      'exaltation': '金星',
      'detriment': '水星',
      'fall': '水星',
      'startDegree': 330.0,
      'endDegree': 360.0,
      'color': Colors.teal,
      'keywords': ['直覺', '同情', '想像', '靈性'],
    },
  ];
  
  /// 根據黃經度數獲取星座信息
  /// 
  /// [longitude] 黃經度數（0-360度）
  /// 
  /// 返回星座信息
  static Map<String, dynamic> getZodiacSign(double longitude) {
    final normalizedLongitude = AngleCalculator.normalizeAngle(longitude);
    
    for (final sign in zodiacSigns) {
      final startDegree = sign['startDegree'] as double;
      final endDegree = sign['endDegree'] as double;
      
      if (endDegree == 360.0) {
        // 雙魚座的特殊情況（330-360度）
        if (normalizedLongitude >= startDegree || normalizedLongitude < 0.0001) {
          return _addPositionInfo(sign, normalizedLongitude);
        }
      } else {
        if (normalizedLongitude >= startDegree && normalizedLongitude < endDegree) {
          return _addPositionInfo(sign, normalizedLongitude);
        }
      }
    }
    
    // 預設返回白羊座（不應該發生）
    return _addPositionInfo(zodiacSigns[0], normalizedLongitude);
  }
  
  /// 添加位置信息到星座數據
  static Map<String, dynamic> _addPositionInfo(
    Map<String, dynamic> sign,
    double longitude,
  ) {
    final startDegree = sign['startDegree'] as double;
    final signPosition = AngleCalculator.normalizeAngle(longitude - startDegree);
    
    // 如果是雙魚座且位置大於30度，調整為正確的星座內位置
    final adjustedPosition = signPosition > 30 ? signPosition - 330 : signPosition;
    
    return {
      ...sign,
      'position': adjustedPosition,
      'positionDMS': AngleCalculator.degreesToDMS(adjustedPosition),
    };
  }
  
  /// 獲取星座的對宮星座
  /// 
  /// [signId] 星座 ID（1-12）
  /// 
  /// 返回對宮星座信息
  static Map<String, dynamic> getOppositeSign(int signId) {
    int oppositeId = signId + 6;
    if (oppositeId > 12) {
      oppositeId -= 12;
    }
    
    return zodiacSigns[oppositeId - 1];
  }
  
  /// 獲取星座的三分相星座
  /// 
  /// [signId] 星座 ID（1-12）
  /// 
  /// 返回三分相星座列表
  static List<Map<String, dynamic>> getTrineSignsigns(int signId) {
    final trineIds = [
      (signId + 4 - 1) % 12 + 1,
      (signId + 8 - 1) % 12 + 1,
    ];
    
    return trineIds.map((id) => zodiacSigns[id - 1]).toList();
  }
  
  /// 獲取星座的四分相星座
  /// 
  /// [signId] 星座 ID（1-12）
  /// 
  /// 返回四分相星座列表
  static List<Map<String, dynamic>> getSquareSigns(int signId) {
    final squareIds = [
      (signId + 3 - 1) % 12 + 1,
      (signId + 9 - 1) % 12 + 1,
    ];
    
    return squareIds.map((id) => zodiacSigns[id - 1]).toList();
  }
  
  /// 獲取星座的六分相星座
  /// 
  /// [signId] 星座 ID（1-12）
  /// 
  /// 返回六分相星座列表
  static List<Map<String, dynamic>> getSextileSigns(int signId) {
    final sextileIds = [
      (signId + 2 - 1) % 12 + 1,
      (signId + 10 - 1) % 12 + 1,
    ];
    
    return sextileIds.map((id) => zodiacSigns[id - 1]).toList();
  }
  
  /// 根據元素獲取星座
  /// 
  /// [element] 元素（火、土、風、水）
  /// 
  /// 返回該元素的星座列表
  static List<Map<String, dynamic>> getSignsByElement(String element) {
    return zodiacSigns.where((sign) => sign['element'] == element).toList();
  }
  
  /// 根據性質獲取星座
  /// 
  /// [quality] 性質（基本、固定、變動）
  /// 
  /// 返回該性質的星座列表
  static List<Map<String, dynamic>> getSignsByQuality(String quality) {
    return zodiacSigns.where((sign) => sign['quality'] == quality).toList();
  }
  
  /// 檢查兩個星座是否相容
  /// 
  /// [signId1] 第一個星座 ID
  /// [signId2] 第二個星座 ID
  /// 
  /// 返回相容性評分（0-10）
  static int getCompatibilityScore(int signId1, int signId2) {
    final sign1 = zodiacSigns[signId1 - 1];
    final sign2 = zodiacSigns[signId2 - 1];
    
    // 相同星座
    if (signId1 == signId2) return 8;
    
    // 相同元素
    if (sign1['element'] == sign2['element']) return 7;
    
    // 互補元素（火-風，土-水）
    final element1 = sign1['element'] as String;
    final element2 = sign2['element'] as String;
    if ((element1 == '火' && element2 == '風') ||
        (element1 == '風' && element2 == '火') ||
        (element1 == '土' && element2 == '水') ||
        (element1 == '水' && element2 == '土')) {
      return 6;
    }
    
    // 三分相
    final trineIds = [
      (signId1 + 4 - 1) % 12 + 1,
      (signId1 + 8 - 1) % 12 + 1,
    ];
    if (trineIds.contains(signId2)) return 8;
    
    // 六分相
    final sextileIds = [
      (signId1 + 2 - 1) % 12 + 1,
      (signId1 + 10 - 1) % 12 + 1,
    ];
    if (sextileIds.contains(signId2)) return 6;
    
    // 對沖
    final oppositeId = (signId1 + 6 - 1) % 12 + 1;
    if (signId2 == oppositeId) return 5;
    
    // 四分相
    final squareIds = [
      (signId1 + 3 - 1) % 12 + 1,
      (signId1 + 9 - 1) % 12 + 1,
    ];
    if (squareIds.contains(signId2)) return 3;
    
    // 其他情況
    return 4;
  }
  
  /// 獲取星座的詳細描述
  /// 
  /// [signId] 星座 ID
  /// 
  /// 返回詳細描述
  static String getSignDescription(int signId) {
    final descriptions = {
      1: '白羊座是黃道第一宮，象徵新的開始和原始的生命力。白羊座的人通常充滿活力、勇敢直接，喜歡挑戰和冒險。',
      2: '金牛座重視穩定和安全感，喜歡美好的事物和感官享受。他們通常很實際，有耐心，但有時也會顯得固執。',
      3: '雙子座好奇心強，善於溝通和學習。他們思維敏捷，適應力強，但有時會顯得不夠專注或深入。',
      4: '巨蟹座情感豐富，重視家庭和安全感。他們直覺敏銳，善於照顧他人，但有時會過於敏感或情緒化。',
      5: '獅子座自信大方，喜歡成為注意的焦點。他們有創造力和領導能力，慷慨大方，但有時會顯得自負。',
      6: '處女座注重細節和完美，善於分析和組織。他們實用可靠，樂於服務他人，但有時會過於挑剔。',
      7: '天秤座追求和諧與平衡，重視美感和公正。他們善於合作和外交，但有時會猶豫不決。',
      8: '天蠍座深沉神秘，情感強烈。他們有洞察力和轉化能力，忠誠專一，但有時會顯得佔有慾強。',
      9: '射手座熱愛自由和探索，樂觀開朗。他們有哲學思維和冒險精神，但有時會缺乏耐心。',
      10: '摩羯座有責任感和野心，重視成就和地位。他們實際可靠，有耐力，但有時會顯得過於嚴肅。',
      11: '水瓶座獨立創新，重視友誼和人道主義。他們思想前衛，但有時會顯得疏離或固執己見。',
      12: '雙魚座富有想像力和同情心，直覺敏銳。他們善解人意，有藝術天賦，但有時會逃避現實。',
    };
    
    return descriptions[signId] ?? '未知星座描述';
  }
}
