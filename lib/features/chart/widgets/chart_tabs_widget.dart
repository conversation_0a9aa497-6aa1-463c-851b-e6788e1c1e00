import 'package:flutter/material.dart';

import '../core/chart_display_controller.dart';

/// 星盤標籤頁 Widget
class ChartTabsWidget extends StatelessWidget {
  final ChartDisplayController controller;
  final TabController tabController;

  const ChartTabsWidget({
    super.key,
    required this.controller,
    required this.tabController,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: controller.theme.primaryColor,
        border: Border(
          bottom: BorderSide(
            color: controller.theme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: TabBar(
        controller: tabController,
        isScrollable: true,
        labelColor: controller.theme.surfaceColor,
        unselectedLabelColor: controller.theme.unselectedLabelColor,
        indicatorColor: controller.theme.secondaryColor,
        labelStyle: controller.theme.labelTextStyle,
        tabs: controller.enabledTabs.map((tabType) {
          return Tab(
            icon: Icon(tabType.icon),
            text: tabType.title,
          );
        }).toList(),
      ),
    );
  }
}
