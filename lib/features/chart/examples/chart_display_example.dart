import 'package:flutter/material.dart';

import '../../../data/models/astrology/chart_data.dart';
import '../../../data/models/astrology/chart_type.dart';
import '../../../data/models/user/birth_data.dart';
import '../chart_display_module.dart';

/// 星盤顯示模組使用示例
class ChartDisplayExample extends StatefulWidget {
  const ChartDisplayExample({super.key});

  @override
  State<ChartDisplayExample> createState() => _ChartDisplayExampleState();
}

class _ChartDisplayExampleState extends State<ChartDisplayExample> {
  late ChartDisplayController _controller;
  int _currentExample = 0;

  final List<String> _exampleTitles = [
    '基本顯示',
    '簡化模式',
    '完整功能',
    '深色主題',
    '自定義配置',
  ];

  @override
  void initState() {
    super.initState();
    _initController();
  }

  void _initController() {
    final config = _getExampleConfig(_currentExample);
    _controller = ChartDisplayController(config: config);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('星盤顯示模組示例'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<int>(
            icon: const Icon(Icons.more_vert),
            onSelected: (index) => _switchExample(index),
            itemBuilder: (context) {
              return _exampleTitles.asMap().entries.map((entry) {
                return PopupMenuItem<int>(
                  value: entry.key,
                  child: Row(
                    children: [
                      if (entry.key == _currentExample)
                        const Icon(Icons.check, size: 16),
                      if (entry.key == _currentExample)
                        const SizedBox(width: 8),
                      Text(entry.value),
                    ],
                  ),
                );
              }).toList();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // 示例選擇器
          Container(
            height: 60,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              border: Border(
                bottom: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.indigo),
                const SizedBox(width: 8),
                Text(
                  '當前示例: ${_exampleTitles[_currentExample]}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: _showConfigDialog,
                  icon: const Icon(Icons.settings, size: 16),
                  label: const Text('設定'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          
          // 星盤顯示區域
          Expanded(
            child: ChartDisplayWidget(
              config: _controller.config,
              controller: _controller,
            ),
          ),
        ],
      ),
    );
  }

  /// 切換示例
  void _switchExample(int index) {
    setState(() {
      _currentExample = index;
      _controller.dispose();
      _initController();
    });
  }

  /// 獲取示例配置
  ChartDisplayConfig _getExampleConfig(int index) {
    final chartData = _createSampleChartData();
    
    switch (index) {
      case 0: // 基本顯示
        return ChartDisplayConfig.defaultConfig(chartData: chartData);
      
      case 1: // 簡化模式
        return ChartDisplayConfig.minimal(chartData: chartData);
      
      case 2: // 完整功能
        return ChartDisplayConfig.full(
          chartData: chartData,
          onPlanetTap: (planet) => _showPlanetInfo(planet),
          onChartTypeChanged: (chartType) => _showMessage('切換到: ${chartType.name}'),
        );
      
      case 3: // 深色主題
        return ChartDisplayConfig(
          chartData: chartData,
          theme: ChartDisplayTheme.darkTheme(),
          settings: ChartDisplaySettings.defaultSettings(),
          onPlanetTap: (planet) => _showPlanetInfo(planet),
        );
      
      case 4: // 自定義配置
        return ChartDisplayConfig(
          chartData: chartData,
          theme: _createCustomTheme(),
          settings: _createCustomSettings(),
          customActions: [
            ChartToolbarAction(
              id: 'info',
              title: '資訊',
              icon: Icons.info,
              onPressed: () => _showChartInfo(),
              tooltip: '顯示星盤資訊',
            ),
            ChartToolbarAction(
              id: 'share',
              title: '分享',
              icon: Icons.share,
              onPressed: () => _shareChart(),
              tooltip: '分享星盤',
            ),
          ],
          onPlanetTap: (planet) => _showPlanetInfo(planet),
          onExportCompleted: (filePath, format) => _showMessage('導出完成: $format'),
          onError: (error) => _showMessage('錯誤: $error'),
        );
      
      default:
        return ChartDisplayConfig.defaultConfig(chartData: chartData);
    }
  }

  /// 創建範例星盤數據
  ChartData _createSampleChartData() {
    return ChartData(
      chartType: ChartType.natal,
      primaryPerson: BirthData(
        id: 'sample_person',
        name: '範例人物',
        dateTime: DateTime(1990, 6, 15, 14, 30),
        birthPlace: '台北市',
        latitude: 25.0330,
        longitude: 121.5654,
      ),
    );
  }

  /// 創建自定義主題
  ChartDisplayTheme _createCustomTheme() {
    return ChartDisplayTheme.defaultTheme().copyWith(
      primaryColor: Colors.purple,
      secondaryColor: Colors.amber,
      backgroundColor: Colors.purple[50],
      borderRadius: 20.0,
      titleTextStyle: const TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.bold,
        color: Colors.purple,
      ),
    );
  }

  /// 創建自定義設定
  ChartDisplaySettings _createCustomSettings() {
    return ChartDisplaySettings(
      enabledTabs: [
        ChartDisplayTabType.chart,
        ChartDisplayTabType.planets,
        ChartDisplayTabType.aspects,
        ChartDisplayTabType.elements,
      ],
      chartCanvas: ChartCanvasSettings(
        showPlanetNames: true,
        showPlanetSymbols: true,
        showAspectLines: true,
        showHouseNumbers: true,
        showDegreeMarks: false,
        enableZoom: true,
        enablePan: true,
        initialZoom: 1.1,
      ),
      planetList: PlanetListSettings.full(),
      houses: HouseSettings.full(),
      aspectTable: AspectTableSettings.full(),
      elements: ChartElementsSettings.full(),
      classical: ClassicalAstrologySettings.defaultSettings(),
      firdaria: FirdariaSettings.defaultSettings(),
      export: ExportSettings.full(),
    );
  }

  /// 顯示行星資訊
  void _showPlanetInfo(dynamic planet) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('行星資訊'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('名稱: ${planet.name}'),
            Text('符號: ${planet.symbol ?? 'N/A'}'),
            Text('經度: ${planet.longitude.toStringAsFixed(2)}°'),
            Text('星座: ${planet.sign}'),
            Text('宮位: ${planet.house}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('關閉'),
          ),
        ],
      ),
    );
  }

  /// 顯示星盤資訊
  void _showChartInfo() {
    final displayData = _controller.displayData;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('星盤資訊'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('標題: ${displayData.title}'),
            Text('副標題: ${displayData.subtitle ?? 'N/A'}'),
            Text('行星數量: ${displayData.planets.length}'),
            Text('相位數量: ${displayData.aspects.length}'),
            Text('星盤類型: ${displayData.chartType.name}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('關閉'),
          ),
        ],
      ),
    );
  }

  /// 分享星盤
  void _shareChart() {
    _showMessage('分享功能（示例）');
  }

  /// 顯示配置對話框
  void _showConfigDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('顯示設定'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('顯示工具列'),
              value: _controller.config.showToolbar,
              onChanged: (value) {
                final newConfig = _controller.config.copyWith(showToolbar: value);
                _controller.updateConfig(newConfig);
                Navigator.pop(context);
              },
            ),
            SwitchListTile(
              title: const Text('顯示標籤頁'),
              value: _controller.config.showTabs,
              onChanged: (value) {
                final newConfig = _controller.config.copyWith(showTabs: value);
                _controller.updateConfig(newConfig);
                Navigator.pop(context);
              },
            ),
            SwitchListTile(
              title: const Text('顯示資訊面板'),
              value: _controller.config.showInfoPanel,
              onChanged: (value) {
                final newConfig = _controller.config.copyWith(showInfoPanel: value);
                _controller.updateConfig(newConfig);
                Navigator.pop(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('關閉'),
          ),
        ],
      ),
    );
  }

  /// 顯示訊息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
