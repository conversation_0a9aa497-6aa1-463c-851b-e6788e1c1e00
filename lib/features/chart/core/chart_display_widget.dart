import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../presentation/viewmodels/chart_viewmodel.dart';
import '../../../presentation/widgets/chart/aspect_table_widget.dart';
import '../../../presentation/widgets/chart/chart_view_widget.dart';
import '../../../presentation/widgets/chart/firdaria_widget.dart';
import '../../../presentation/widgets/chart/houses_widget.dart';
import '../../../presentation/widgets/chart/planet_list_widget.dart';
import '../../../shared/widgets/chart_elements_widget.dart';
import '../../../shared/widgets/classical_astrology_widget.dart';
import '../models/chart_display_settings.dart';
import '../widgets/chart_tabs_widget.dart';
import 'chart_display_config.dart';
import 'chart_display_controller.dart';

/// 星盤顯示主要 Widget
/// 
/// 這是星盤顯示模組的入口點，提供完整的星盤顯示功能
class ChartDisplayWidget extends StatefulWidget {
  /// 配置
  final ChartDisplayConfig config;
  
  /// 控制器（可選，如果不提供會自動創建）
  final ChartDisplayController? controller;

  const ChartDisplayWidget({
    super.key,
    required this.config,
    this.controller,
  });

  @override
  State<ChartDisplayWidget> createState() => _ChartDisplayWidgetState();
}

class _ChartDisplayWidgetState extends State<ChartDisplayWidget>
    with TickerProviderStateMixin {
  late ChartDisplayController _controller;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    
    // 初始化控制器
    _controller = widget.controller ?? ChartDisplayController(config: widget.config);
    
    // 初始化標籤頁控制器
    _initTabController();
  }

  @override
  void didUpdateWidget(ChartDisplayWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 如果配置變更，更新控制器
    if (widget.config != oldWidget.config) {
      _controller.updateConfig(widget.config);
    }
    
    // 如果標籤頁數量變更，重新初始化標籤頁控制器
    if (_controller.enabledTabs.length != _tabController.length) {
      _tabController.dispose();
      _initTabController();
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  /// 初始化標籤頁控制器
  void _initTabController() {
    _tabController = TabController(
      length: _controller.enabledTabs.length,
      vsync: this,
      initialIndex: _controller.currentTabIndex,
    );
    
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        _controller.switchTab(_tabController.index);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _controller,
      child: Consumer<ChartDisplayController>(
        builder: (context, controller, child) {
          return Container(
            decoration: BoxDecoration(
              color: controller.theme.backgroundColor,
              borderRadius: BorderRadius.circular(controller.theme.borderRadius),
            ),
            child: Column(
              children: [
                // 工具列
                // if (controller.config.showToolbar)
                //   ChartToolbarWidget(controller: controller),
                
                // 標籤頁
                if (controller.config.showTabs && controller.enabledTabs.length > 1)
                  ChartTabsWidget(
                    controller: controller,
                    tabController: _tabController,
                  ),
                
                // 主要內容區域
                Expanded(
                  child: _buildContent(controller),
                ),
                
                // 資訊面板
                // if (controller.config.showInfoPanel)
                //   ChartInfoPanel(controller: controller),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 構建內容區域
  Widget _buildContent(ChartDisplayController controller) {
    if (controller.isLoading) {
      return _buildLoadingState(controller);
    }
    
    if (controller.error != null) {
      return _buildErrorState(controller);
    }
    
    if (!controller.displayData.isValid) {
      return _buildEmptyState(controller);
    }
    
    if (controller.config.showTabs && controller.enabledTabs.length > 1) {
      return TabBarView(
        controller: _tabController,
        children: controller.enabledTabs.map((tabType) {
          return _buildTabContent(controller, tabType);
        }).toList(),
      );
    } else {
      // 如果只有一個標籤頁或不顯示標籤頁，直接顯示內容
      final tabType = controller.enabledTabs.isNotEmpty 
          ? controller.enabledTabs.first 
          : ChartDisplayTabType.chart;
      return _buildTabContent(controller, tabType);
    }
  }

  /// 構建標籤頁內容
  Widget _buildTabContent(ChartDisplayController controller, ChartDisplayTabType tabType) {
    // 從配置中獲取 ChartViewModel
    final chartViewModel = _getChartViewModel(controller);

    if (chartViewModel == null) {
      return _buildPlaceholderContent(controller, '無法獲取星盤數據', Icons.error);
    }

    switch (tabType) {
      case ChartDisplayTabType.chart:
        return ChartViewWidget(viewModel: chartViewModel);

      case ChartDisplayTabType.firdaria:
        return FirdariaWidget(viewModel: chartViewModel);

      case ChartDisplayTabType.planets:
        return PlanetListWidget(viewModel: chartViewModel);

      case ChartDisplayTabType.houses:
        return HousesWidget(viewModel: chartViewModel);

      case ChartDisplayTabType.aspects:
        return AspectTableWidget(viewModel: chartViewModel);

      case ChartDisplayTabType.elements:
        return ChartElementsWidget(viewModel: chartViewModel);

      case ChartDisplayTabType.classical:
        return ClassicalAstrologyWidget(viewModel: chartViewModel);
    }
  }

  /// 從控制器配置中獲取 ChartViewModel
  ChartViewModel? _getChartViewModel(ChartDisplayController controller) {
    // 如果配置中包含 ChartViewModel，直接返回
    if (controller.config.chartViewModel != null) {
      return controller.config.chartViewModel;
    }

    // 嘗試從 Provider 中獲取
    try {
      return Provider.of<ChartViewModel>(context, listen: false);
    } catch (e) {
      // 如果無法從 Provider 獲取，創建一個臨時的 ViewModel
      return _createTemporaryViewModel(controller);
    }
  }

  /// 創建臨時的 ChartViewModel
  ChartViewModel? _createTemporaryViewModel(ChartDisplayController controller) {
    if (controller.config.chartData == null) {
      return null;
    }

    // 創建一個臨時的 ChartViewModel 來顯示數據
    final viewModel = ChartViewModel();
    // 使用 updateChartData 方法來設置數據
    viewModel.updateChartData(controller.config.chartData!);
    return viewModel;
  }

  /// 構建佔位符內容（暫時用於演示）
  Widget _buildPlaceholderContent(ChartDisplayController controller, String title, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: controller.theme.secondaryTextColor,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: controller.theme.titleTextStyle,
          ),
          const SizedBox(height: 8),
          Text(
            '此功能正在開發中',
            style: controller.theme.bodyTextStyle,
          ),
        ],
      ),
    );
  }

  /// 構建載入狀態
  Widget _buildLoadingState(ChartDisplayController controller) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: controller.theme.primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            '正在載入星盤數據...',
            style: controller.theme.bodyTextStyle,
          ),
        ],
      ),
    );
  }

  /// 構建錯誤狀態
  Widget _buildErrorState(ChartDisplayController controller) {
    return Center(
      child: Padding(
        padding: controller.theme.padding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: controller.theme.errorColor,
            ),
            const SizedBox(height: 16),
            Text(
              '載入失敗',
              style: controller.theme.titleTextStyle.copyWith(
                color: controller.theme.errorColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              controller.error ?? '未知錯誤',
              style: controller.theme.bodyTextStyle,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => controller.refresh(),
              style: ElevatedButton.styleFrom(
                backgroundColor: controller.theme.primaryColor,
              ),
              child: const Text('重試'),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState(ChartDisplayController controller) {
    return Center(
      child: Padding(
        padding: controller.theme.padding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.circle_outlined,
              size: 64,
              color: controller.theme.secondaryTextColor,
            ),
            const SizedBox(height: 16),
            Text(
              '暫無星盤數據',
              style: controller.theme.titleTextStyle.copyWith(
                color: controller.theme.secondaryTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '請提供有效的星盤數據',
              style: controller.theme.bodyTextStyle,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
