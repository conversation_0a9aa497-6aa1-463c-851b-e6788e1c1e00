import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../chart_display_module.dart';


/// ChartPage 整合示例
/// 
/// 展示如何將新的星盤顯示模組整合到現有的 ChartPage 中
class ChartPageIntegration extends StatefulWidget {
  final ChartData chartData;
  final bool isMinimalMode;

  const ChartPageIntegration({
    super.key,
    required this.chartData,
    this.isMinimalMode = false,
  });

  @override
  State<ChartPageIntegration> createState() => _ChartPageIntegrationState();
}

class _ChartPageIntegrationState extends State<ChartPageIntegration> {
  late ChartDisplayController _controller;
  bool _useNewModule = true;

  @override
  void initState() {
    super.initState();
    _initController();
  }

  void _initController() {
    final config = widget.isMinimalMode
        ? _createMinimalConfig()
        : _createFullConfig();
    
    _controller = ChartDisplayController(config: config);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.chartData.primaryPerson.name),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          // 切換顯示模式
          IconButton(
            icon: Icon(_useNewModule ? Icons.toggle_on : Icons.toggle_off),
            onPressed: () {
              setState(() {
                _useNewModule = !_useNewModule;
              });
            },
            tooltip: _useNewModule ? '使用新模組' : '使用舊版本',
          ),
          // 設定按鈕
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettings,
            tooltip: '設定',
          ),
        ],
      ),
      body: _useNewModule ? _buildNewModuleView() : _buildLegacyView(),
    );
  }

  /// 使用新模組的視圖
  Widget _buildNewModuleView() {
    return ChartDisplayWidget(
      config: _controller.config,
      controller: _controller,
    );
  }

  /// 舊版本的視圖（佔位符）
  Widget _buildLegacyView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.hourglass_empty,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '舊版 ChartPage',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '這裡會顯示原來的 ChartPage 內容',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// 創建簡化配置
  ChartDisplayConfig _createMinimalConfig() {
    return ChartDisplayConfig.minimal(
      chartData: widget.chartData,
      theme: _createAppTheme(),
    );
  }

  /// 創建完整配置
  ChartDisplayConfig _createFullConfig() {
    return ChartDisplayConfig.full(
      chartData: widget.chartData,
      theme: _createAppTheme(),
      customActions: [
        ChartToolbarAction(
          id: 'interpretation',
          title: '解讀',
          icon: Icons.psychology,
          onPressed: _startInterpretation,
          tooltip: '開始解讀',
        ),
        ChartToolbarAction(
          id: 'compare',
          title: '比較',
          icon: Icons.compare_arrows,
          onPressed: _compareCharts,
          tooltip: '與其他星盤比較',
        ),
      ],
      onPlanetTap: _handlePlanetTap,
      onChartTypeChanged: _handleChartTypeChanged,
    );
  }

  /// 創建符合 App 主題的星盤主題
  ChartDisplayTheme _createAppTheme() {
    return ChartDisplayTheme(
      primaryColor: AppColors.royalIndigo,
      secondaryColor: AppColors.solarAmber,
      backgroundColor: AppColors.indigoSurface,
      surfaceColor: Colors.white,
      textColor: AppColors.textPrimary,
      secondaryTextColor: AppColors.textSecondary,
      borderColor: AppColors.indigoLight,
      dividerColor: AppColors.indigoLight.withValues(alpha: 0.5),
      unselectedLabelColor: Colors.white70,
      errorColor: Colors.red,
      successColor: Colors.green,
      warningColor: Colors.orange,
      titleTextStyle: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
      subtitleTextStyle: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: AppColors.textPrimary,
      ),
      bodyTextStyle: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.normal,
        color: AppColors.textSecondary,
      ),
      labelTextStyle: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: AppColors.textSecondary,
      ),
      borderRadius: 12.0,
      shadows: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
      padding: const EdgeInsets.all(16.0),
      margin: const EdgeInsets.all(8.0),
    );
  }

  /// 處理行星點擊
  void _handlePlanetTap(dynamic planet) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${planet.name} 詳細資訊'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('位置', '${planet.longitude.toStringAsFixed(2)}°'),
            _buildInfoRow('星座', planet.sign),
            _buildInfoRow('宮位', '第${planet.house}宮'),
            if (planet.symbol != null)
              _buildInfoRow('符號', planet.symbol),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('關閉'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _startPlanetInterpretation(planet);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.royalIndigo,
              foregroundColor: Colors.white,
            ),
            child: const Text('解讀'),
          ),
        ],
      ),
    );
  }

  /// 構建資訊行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// 處理星盤類型變更
  void _handleChartTypeChanged(dynamic chartType) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('切換到: ${chartType.name}'),
        backgroundColor: AppColors.royalIndigo,
      ),
    );
  }

  /// 開始 AI 解讀
  void _startInterpretation() {
    // 這裡整合現有的 AI 解讀功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('正在準備解讀...'),
      ),
    );
  }

  /// 開始行星 AI 解讀
  void _startPlanetInterpretation(dynamic planet) {
    // 這裡整合現有的行星解讀功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('正在解讀 ${planet.name}...'),
      ),
    );
  }

  /// 比較星盤
  void _compareCharts() {
    // 這裡整合現有的星盤比較功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('正在準備星盤比較...'),
      ),
    );
  }

  /// 顯示設定對話框
  void _showSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('顯示設定'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('顯示工具列'),
              value: _controller.config.showToolbar,
              onChanged: (value) {
                final newConfig = _controller.config.copyWith(showToolbar: value);
                _controller.updateConfig(newConfig);
                Navigator.pop(context);
              },
            ),
            SwitchListTile(
              title: const Text('顯示標籤頁'),
              value: _controller.config.showTabs,
              onChanged: (value) {
                final newConfig = _controller.config.copyWith(showTabs: value);
                _controller.updateConfig(newConfig);
                Navigator.pop(context);
              },
            ),
            SwitchListTile(
              title: const Text('顯示資訊面板'),
              value: _controller.config.showInfoPanel,
              onChanged: (value) {
                final newConfig = _controller.config.copyWith(showInfoPanel: value);
                _controller.updateConfig(newConfig);
                Navigator.pop(context);
              },
            ),
            SwitchListTile(
              title: const Text('啟用縮放'),
              value: _controller.config.enableZoom,
              onChanged: (value) {
                final newConfig = _controller.config.copyWith(enableZoom: value);
                _controller.updateConfig(newConfig);
                Navigator.pop(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('關閉'),
          ),
        ],
      ),
    );
  }
}
