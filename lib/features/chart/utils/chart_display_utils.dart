import 'dart:math' as math;

import 'package:flutter/material.dart';

import '../models/chart_display_data.dart';
import '../models/chart_display_settings.dart';
import '../models/chart_display_theme.dart';

/// 星盤顯示工具類
class ChartDisplayUtils {
  /// 格式化日期時間
  static String formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 格式化度數
  static String formatDegree(double degree) {
    final degrees = degree.floor();
    final minutes = ((degree - degrees) * 60).floor();
    final seconds = (((degree - degrees) * 60 - minutes) * 60).floor();
    
    return '$degrees°${minutes.toString().padLeft(2, '0')}\'${seconds.toString().padLeft(2, '0')}"';
  }

  /// 格式化經緯度
  static String formatCoordinate(double coordinate, bool isLatitude) {
    final abs = coordinate.abs();
    final degrees = abs.floor();
    final minutes = ((abs - degrees) * 60).floor();
    final seconds = (((abs - degrees) * 60 - minutes) * 60).floor();
    
    final direction = isLatitude 
        ? (coordinate >= 0 ? 'N' : 'S')
        : (coordinate >= 0 ? 'E' : 'W');
    
    return '$degrees°${minutes.toString().padLeft(2, '0')}\'${seconds.toString().padLeft(2, '0')}"$direction';
  }

  /// 計算兩個角度之間的差值
  static double calculateAngleDifference(double angle1, double angle2) {
    double diff = (angle2 - angle1).abs();
    if (diff > 180) {
      diff = 360 - diff;
    }
    return diff;
  }

  /// 標準化角度到 0-360 範圍
  static double normalizeAngle(double angle) {
    while (angle < 0) {
      angle += 360;
    }
    while (angle >= 360) {
      angle -= 360;
    }
    return angle;
  }

  /// 將角度轉換為弧度
  static double degreesToRadians(double degrees) {
    return degrees * (3.14159265359 / 180.0);
  }

  /// 將弧度轉換為角度
  static double radiansToDegrees(double radians) {
    return radians * (180.0 / 3.14159265359);
  }

  /// 獲取星座符號
  static String getZodiacSymbol(String signName) {
    const symbols = {
      '白羊座': '♈',
      '金牛座': '♉',
      '雙子座': '♊',
      '巨蟹座': '♋',
      '獅子座': '♌',
      '處女座': '♍',
      '天秤座': '♎',
      '天蠍座': '♏',
      '射手座': '♐',
      '摩羯座': '♑',
      '水瓶座': '♒',
      '雙魚座': '♓',
    };
    return symbols[signName] ?? signName;
  }

  /// 獲取行星符號
  static String getPlanetSymbol(String planetName) {
    const symbols = {
      '太陽': '☉',
      '月亮': '☽',
      '水星': '☿',
      '金星': '♀',
      '火星': '♂',
      '木星': '♃',
      '土星': '♄',
      '天王星': '♅',
      '海王星': '♆',
      '冥王星': '♇',
      '北交點': '☊',
      '南交點': '☋',
      '凱龍星': '⚷',
    };
    return symbols[planetName] ?? planetName;
  }

  /// 獲取相位符號
  static String getAspectSymbol(String aspectName) {
    const symbols = {
      '合相': '☌',
      '對沖': '☍',
      '三分相': '△',
      '四分相': '□',
      '六分相': '⚹',
      '半四分相': '∠',
      '半三分相': '⚺',
    };
    return symbols[aspectName] ?? aspectName;
  }

  /// 獲取相位顏色
  static Color getAspectColor(String aspectName, ChartDisplayTheme theme) {
    switch (aspectName) {
      case '合相':
        return theme.primaryColor;
      case '對沖':
        return theme.errorColor;
      case '三分相':
        return theme.successColor;
      case '四分相':
        return theme.warningColor;
      case '六分相':
        return theme.secondaryColor;
      default:
        return theme.secondaryTextColor;
    }
  }

  /// 檢查角度是否在容許度範圍內
  static bool isWithinOrb(double actualAngle, double targetAngle, double orb) {
    final diff = calculateAngleDifference(actualAngle, targetAngle);
    return diff <= orb;
  }

  /// 計算行星在圓周上的位置
  static Offset calculatePlanetPosition(
    Offset center,
    double radius,
    double longitude,
  ) {
    final angle = degreesToRadians(longitude - 90); // 調整起始角度
    return Offset(
      center.dx + radius * math.cos(angle),
      center.dy + radius * math.sin(angle),
    );
  }

  /// 計算文字的最佳位置（避免重疊）
  static Offset calculateTextPosition(
    Offset basePosition,
    Size textSize,
    Size containerSize,
  ) {
    double x = basePosition.dx - textSize.width / 2;
    double y = basePosition.dy - textSize.height / 2;
    
    // 確保文字不超出容器邊界
    x = x.clamp(0, containerSize.width - textSize.width);
    y = y.clamp(0, containerSize.height - textSize.height);
    
    return Offset(x, y);
  }

  /// 生成顏色漸變
  static List<Color> generateColorGradient(
    Color startColor,
    Color endColor,
    int steps,
  ) {
    final colors = <Color>[];
    
    for (int i = 0; i < steps; i++) {
      final ratio = i / (steps - 1);
      final color = Color.lerp(startColor, endColor, ratio)!;
      colors.add(color);
    }
    
    return colors;
  }

  /// 計算對比色
  static Color getContrastColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  /// 驗證配置的完整性
  static bool validateConfiguration(
    ChartDisplayData displayData,
    ChartDisplaySettings settings,
  ) {
    // 檢查數據完整性
    if (!displayData.isValid) {
      return false;
    }
    
    // 檢查標籤頁設定
    if (settings.enabledTabs.isEmpty) {
      return false;
    }
    
    // 檢查功能兼容性
    for (final tab in settings.enabledTabs) {
      if (tab == ChartDisplayTabType.firdaria && !displayData.isFirdariaChart) {
        return false;
      }
    }
    
    return true;
  }

  /// 生成唯一的 ID
  static String generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// 深拷貝 Map
  static Map<String, dynamic> deepCopyMap(Map<String, dynamic> original) {
    final copy = <String, dynamic>{};
    
    for (final entry in original.entries) {
      if (entry.value is Map<String, dynamic>) {
        copy[entry.key] = deepCopyMap(entry.value);
      } else if (entry.value is List) {
        copy[entry.key] = List.from(entry.value);
      } else {
        copy[entry.key] = entry.value;
      }
    }
    
    return copy;
  }

  /// 安全的類型轉換
  static T? safeCast<T>(dynamic value) {
    try {
      return value as T?;
    } catch (e) {
      return null;
    }
  }

  /// 格式化文件大小
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  /// 生成隨機顏色
  static Color generateRandomColor() {
    final random = math.Random();
    return Color.fromARGB(
      255,
      random.nextInt(256),
      random.nextInt(256),
      random.nextInt(256),
    );
  }
}
