# 用戶設備追蹤功能實現文件

## 📋 概述

為了更好地了解用戶的使用情況和提供更精準的服務，我們在用戶列表中新增了 IP 地址和平台信息的追蹤功能。這些信息會在用戶登入時自動更新，並在管理員的用戶列表頁面中顯示。

## 🎯 主要功能

### 1. 用戶資料模型擴展
- **新增欄位**: `lastLoginIp` (最後登入 IP 地址)
- **新增欄位**: `platform` (平台信息)
- **自動更新**: 在用戶登入時自動更新這些信息

### 2. 設備信息服務
- **檔案位置**: `lib/shared/utils/device_info_service.dart`
- **功能**: 獲取用戶的公網 IP 地址和詳細平台信息
- **快取機制**: IP 地址快取 5 分鐘，平台信息永久快取

### 3. 用戶設備追蹤服務
- **檔案位置**: `lib/data/services/api/user_device_tracking_service.dart`
- **功能**: 在用戶登入時更新設備信息
- **異步處理**: 不阻塞登入流程

## 🔧 技術實現

### UserProfile 模型更新

新增了兩個可選欄位：

```dart
final String? lastLoginIp; // 最後登入 IP 地址
final String? platform; // 平台信息 (web, android, ios, etc.)
```

### 設備信息服務 (DeviceInfoService)

#### 主要方法

1. **`getPlatformInfo()`** - 獲取詳細平台信息
   ```dart
   // 範例輸出:
   // "Web (Chrome)"
   // "Android 13 (Pixel 7)"
   // "iOS 16.5 (iPhone)"
   // "Windows 11 (Windows 11 Pro)"
   ```

2. **`getPublicIpAddress()`** - 獲取公網 IP 地址
   - 使用多個 IP 檢測服務提高成功率
   - 支援 IPv4 和 IPv6 格式驗證
   - 5 分鐘快取機制

3. **`getUserDeviceAndNetworkInfo()`** - 一次性獲取所有信息

#### IP 檢測服務

使用以下服務確保高可用性：
- `https://api.ipify.org?format=json`
- `https://httpbin.org/ip`
- `https://api.myip.com`
- `https://ipapi.co/json/`

### 用戶設備追蹤服務 (UserDeviceTrackingService)

#### 主要方法

1. **`updateUserDeviceInfo()`** - 更新用戶設備信息
2. **`onUserLogin()`** - 用戶登入時調用
3. **`batchUpdateAllUsersDeviceInfo()`** - 批量更新所有用戶（管理員功能）
4. **`getDeviceInfoStatistics()`** - 獲取設備信息統計

### 登入流程整合

在 `FirebaseAuthService` 的兩個關鍵方法中添加了設備追蹤：

1. **`_saveUserSessionSDK()`** - SDK 版本的會話保存
2. **`_saveUserSession()`** - REST API 版本的會話保存

```dart
// 更新用戶設備信息（異步執行，不阻塞登入流程）
UserDeviceTrackingService.onUserLogin(user.uid);
```

## 📱 用戶列表頁面更新

### 新增排序選項

- **最後登入 IP** (`UserSortOption.lastLoginIp`)
- **平台** (`UserSortOption.platform`)

### 搜尋功能擴展

現在可以搜尋：
- Email
- 用戶名稱
- UID
- **IP 地址** (新增)
- **平台信息** (新增)

### 顯示改進

在用戶卡片中新增顯示：
- 最後登入 IP 地址
- 平台信息

只有當這些信息存在時才會顯示，避免空白內容。

## 🎨 UI 改進

### 搜尋提示更新
```dart
hintText: '搜尋用戶 (Email、名稱、UID、IP、平台)'
```

### 排序圖標
- **IP 地址**: `Icons.public`
- **平台**: `Icons.devices`

### 信息顯示
```dart
if (userProfile.lastLoginIp != null && userProfile.lastLoginIp!.isNotEmpty)
  _buildInfoRow('最後登入 IP', userProfile.lastLoginIp!),
if (userProfile.platform != null && userProfile.platform!.isNotEmpty)
  _buildInfoRow('平台', userProfile.platform!),
```

## 🔄 應用啟動整合

在 `AppInitializationService` 中添加了設備信息服務的初始化：

```dart
// 4. 初始化設備信息服務
await DeviceInfoService.initialize();
```

## 📊 數據範例

### 平台信息範例
- `Web (Chrome)`
- `Android 13 (Samsung SM-G998B)`
- `iOS 16.5 (iPhone14,2)`
- `Windows 11 (Windows 11 Pro)`
- `macOS 13.4 (MacBookPro18,1)`

### IP 地址範例
- `*************` (IPv4)
- `2001:db8::1` (IPv6)

## 🛡️ 隱私和安全考量

### IP 地址處理
- 只儲存最後登入的 IP 地址
- 不記錄 IP 地址歷史
- 用於統計和安全分析

### 平台信息
- 不包含敏感的設備識別信息
- 主要用於了解用戶使用習慣
- 幫助優化不同平台的體驗

### 數據保護
- 所有信息都加密儲存在 Firebase
- 遵循 GDPR 和相關隱私法規
- 用戶可以要求刪除這些信息

## 🚀 效益

### 對管理員
1. **用戶分析**: 了解用戶的設備和網路環境
2. **安全監控**: 檢測異常登入行為
3. **平台優化**: 根據用戶平台分佈優化開發重點
4. **技術支援**: 更好地協助用戶解決平台特定問題

### 對開發團隊
1. **使用統計**: 了解哪些平台最受歡迎
2. **錯誤追蹤**: 平台特定的錯誤分析
3. **功能規劃**: 基於實際使用數據的功能開發
4. **性能優化**: 針對主要平台進行優化

## 🔍 統計功能

`UserDeviceTrackingService.getDeviceInfoStatistics()` 提供：

```dart
{
  'total_users': 1000,
  'users_with_platform': 950,
  'users_with_ip': 900,
  'platform_distribution': {
    'Web': 400,
    'Android': 350,
    'iOS': 200,
    'Windows': 50,
  },
  'recent_logins': 150, // 最近24小時
}
```

## 🔧 管理員功能

### 批量更新
```dart
final result = await UserDeviceTrackingService.batchUpdateAllUsersDeviceInfo();
// 返回: {'total': 1000, 'success': 950, 'failure': 50}
```

### 快取管理
```dart
await UserDeviceTrackingService.clearExpiredCache();
```

## 📈 未來擴展

1. **地理位置**: 基於 IP 地址的大概位置信息
2. **設備指紋**: 更詳細的設備識別（在用戶同意下）
3. **使用模式分析**: 不同平台的使用習慣分析
4. **安全警報**: 異常登入位置或設備的警報
5. **個性化體驗**: 基於平台的 UI 優化

## 🐛 故障排除

### 常見問題

1. **IP 地址獲取失敗**
   - 檢查網路連接
   - 確認 IP 檢測服務可用性
   - 查看日誌中的錯誤信息

2. **平台信息不準確**
   - 確認 `device_info_plus` 套件版本
   - 檢查不同平台的權限設置

3. **設備信息未更新**
   - 確認用戶已重新登入
   - 檢查 `UserDeviceTrackingService` 是否正確調用

### 日誌監控

關鍵日誌信息：
- `設備信息服務初始化成功`
- `獲取公網 IP 地址成功: [IP] (來源: [服務])`
- `獲取平台信息: [平台信息]`
- `用戶登入，更新設備信息: [用戶ID]`

## 📝 注意事項

1. **性能影響**: IP 地址獲取可能需要幾秒鐘，但不會阻塞登入
2. **網路依賴**: IP 地址獲取需要網路連接
3. **隱私合規**: 確保符合當地隱私法規
4. **數據清理**: 定期清理過期的設備信息快取
