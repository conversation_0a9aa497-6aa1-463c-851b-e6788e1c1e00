# 登入狀態管理優化

## 概述

本次修改優化了設定頁面的登入狀態管理，確保用戶登入成功後能立即看到狀態更新，管理者用戶能即時看到管理者選項。

## 主要修改

### 1. 設定頁面狀態刷新機制

**文件**: `lib/presentation/pages/main/settings_page.dart`

#### 新增方法

```dart
/// 刷新所有狀態（登入成功後調用）
Future<void> _refreshAllStates() async {
  logger.i('刷新設定頁面所有狀態');
  await Future.wait([
    _loadUserStatus(),
    _loadHiddenAnnouncementsStats(),
  ]);
}
```

#### 優化登入導航

```dart
/// 導航到登入頁面
void _navigateToLogin() {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => const FirebaseLoginPage(),
    ),
  ).then((result) {
    // 登入成功後重新載入用戶狀態
    if (result != null) {
      logger.i('登入成功，重新載入設定頁面狀態');
      _refreshAllStates();
    }
  });
}
```

#### 優化登出處理

```dart
/// 顯示登出確認對話框
void _showLogoutDialog(AuthViewModel authViewModel) {
  // ... 對話框代碼 ...
  TextButton(
    onPressed: () async {
      Navigator.of(context).pop();
      
      // 執行登出
      await authViewModel.signOut();
      
      // 登出後刷新狀態
      logger.i('登出成功，重新載入設定頁面狀態');
      _refreshAllStates();
    },
    child: const Text('登出'),
  ),
}
```

#### 優化用戶資料頁面導航

```dart
/// 導航到用戶資料頁面
void _navigateToUserProfile() {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => const UserProfilePage(),
    ),
  ).then((_) {
    // 從用戶資料頁面返回後刷新狀態
    logger.d('從用戶資料頁面返回，刷新設定頁面狀態');
    _refreshAllStates();
  });
}
```

### 2. 用戶狀態載入優化

#### 增強日誌記錄

```dart
/// 載入用戶狀態
Future<void> _loadUserStatus() async {
  try {
    logger.d('開始載入用戶狀態');
    
    // ... 載入邏輯 ...
    
    if (currentUser != null) {
      logger.d('當前用戶: ${currentUser.uid}');
      userProfile = await UserProfileUnifiedService.getUserById(currentUser.uid);
      
      // 記錄管理者狀態
      if (userProfile?.isAdmin == true) {
        logger.i('檢測到管理者用戶: ${currentUser.uid}');
      }
    } else {
      logger.d('未檢測到登入用戶');
    }
    
    // ... 狀態更新 ...
    
    // 記錄狀態更新完成
    logger.d('用戶狀態載入完成，管理者狀態: ${userProfile?.isAdmin}');
  } catch (e) {
    logger.e('載入用戶狀態失敗: $e');
    // ... 錯誤處理 ...
  }
}
```

## 功能特點

### 1. 即時狀態更新

- **登入成功後**: 自動刷新用戶狀態，立即顯示登入狀態和用戶信息
- **登出後**: 清除用戶狀態，隱藏需要登入的功能選項
- **頁面返回**: 從相關頁面返回後自動刷新狀態

### 2. 管理者權限顯示

- **權限檢查**: 基於 `UserProfile.isAdmin` 屬性
- **即時顯示**: 管理者登入後立即顯示管理者選項
- **安全性**: 只有確認的管理者才能看到管理功能

### 3. 日誌記錄

- **詳細日誌**: 記錄狀態載入過程和結果
- **錯誤追蹤**: 記錄載入失敗的詳細信息
- **調試支援**: 便於開發和維護時的問題診斷

## 管理者功能

當用戶具有管理者權限時，設定頁面會顯示以下額外選項：

### 管理者選項
- **管理後台**: 系統管理與監控中心
- **日誌管理**: 查看應用程式日誌、上傳診斷資料和管理日誌設定

### 開發者選項
- **API Key 設定**: 管理 AI 模型的 API 密鑰
- **AI 模型設定**: 配置 AI 模型參數
- **解讀指引設定**: 管理解讀指引內容

## 技術實現

### 狀態管理架構

```
AuthViewModel (認證狀態)
    ↓
SettingsPage (UI 狀態)
    ↓
UserProfile (用戶資料)
    ↓
管理者權限檢查
```

### 狀態同步機制

1. **AuthViewModel**: 監聽 Firebase 認證狀態變化
2. **SettingsPage**: 響應認證狀態變化，刷新本地狀態
3. **UserProfile**: 從 Firestore 載入最新的用戶資料
4. **UI 更新**: 基於最新狀態重新渲染界面

## 測試建議

### 功能測試

1. **登入流程測試**
   - 從設定頁面點擊登入
   - 完成登入後檢查狀態是否立即更新
   - 驗證管理者選項是否正確顯示

2. **登出流程測試**
   - 點擊登出確認
   - 檢查狀態是否立即清除
   - 驗證管理者選項是否正確隱藏

3. **頁面導航測試**
   - 導航到用戶資料頁面後返回
   - 檢查狀態是否保持同步

### 權限測試

1. **一般用戶**: 不應看到管理者選項
2. **管理者用戶**: 應看到完整的管理者選項
3. **匿名用戶**: 應看到登入提示

## 注意事項

1. **網路連接**: 狀態刷新需要網路連接來載入 Firestore 資料
2. **錯誤處理**: 載入失敗時會記錄錯誤但不會阻止 UI 顯示
3. **性能考量**: 使用 `Future.wait()` 並行載入多個狀態以提高效率
4. **日誌管理**: 適當的日誌記錄有助於問題診斷但不會影響用戶體驗
