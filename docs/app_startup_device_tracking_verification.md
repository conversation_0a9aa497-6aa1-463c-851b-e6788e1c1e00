# 應用啟動時設備追蹤功能驗證文件

## 📋 概述

為了確保在用戶已登入的狀態下，應用啟動時會自動更新 Firestore 中的 user_profiles 集合的設備信息，我們在 `AuthViewModel` 中添加了相應的功能。

## 🔧 實現細節

### 修改的檔案

#### 1. `lib/main.dart`

**關鍵修改**:
```dart
// 修改前（懶加載，問題所在）
ChangeNotifierProvider(create: (_) => AuthViewModel()),

// 修改後（立即初始化）
ChangeNotifierProvider(create: (_) => AuthViewModel(), lazy: false),
```

#### 2. `lib/presentation/viewmodels/auth_viewmodel.dart`

**新增導入**:
```dart
import '../../data/services/api/user_device_tracking_service.dart';
```

**修改的方法**:

1. **`_initializeAuth()`** - 應用啟動時的認證初始化
   - 檢測到已登入用戶時，調用 `_updateUserDeviceInfoOnStartup()`
   - 如果未檢測到用戶，調用 `_delayedAuthCheck()` 延遲檢查
   - 使用 `forceUpdate: false` 避免不必要的更新

2. **認證狀態監聽器** - 用戶登入狀態變化時
   - 用戶登入時調用 `_updateUserDeviceInfoOnLogin()`
   - 使用專門的登入處理方法

**新增的方法**:

1. **`_delayedAuthCheck()`** - 延遲檢查認證狀態
   ```dart
   void _delayedAuthCheck() {
     // 延遲 2 秒後再次檢查，給 Firebase 認證服務時間完成初始化
     Future.delayed(const Duration(seconds: 2), () {
       final currentUser = AuthService.getCurrentUser();
       if (currentUser != null && _currentUser == null) {
         // 發現已登入用戶，更新狀態和設備信息
         _currentUser = currentUser;
         _updateAuthState(AuthState.authenticated(currentUser.uid));
         _updateUserDeviceInfoOnStartup(currentUser.uid);
       }
     });
   }
   ```

2. **`_updateUserDeviceInfoOnStartup(String userId)`**
   ```dart
   void _updateUserDeviceInfoOnStartup(String userId) {
     // 異步更新設備信息，不阻塞 UI 初始化
     Future.microtask(() async {
       await UserDeviceTrackingService.updateUserDeviceInfo(
         userId,
         forceUpdate: false, // 不強制更新
       );
     });
   }
   ```

3. **`_updateUserDeviceInfoOnLogin(String userId)`**
   ```dart
   void _updateUserDeviceInfoOnLogin(String userId) {
     // 使用專門的登入處理方法
     UserDeviceTrackingService.onUserLogin(userId);
   }
   ```

### 🔍 問題解決

#### 問題 1：AuthViewModel 懶加載問題
**問題**: `AuthViewModel` 使用 `ChangeNotifierProvider` 的默認懶加載模式，只有當有 Widget 實際使用時才會創建實例，導致 `_initializeAuth()` 方法在應用啟動時不會執行。

**解決方案**: 在 `main.dart` 中將 `AuthViewModel` 的 `ChangeNotifierProvider` 設置為非懶加載模式：
```dart
ChangeNotifierProvider(create: (_) => AuthViewModel(), lazy: false), // 立即初始化
```

#### 問題 2：初始化時序問題
**問題**: `AuthViewModel` 在應用啟動時立即初始化，但 `FirebaseAuthService.initialize()` 在背景服務中執行，導致 `getCurrentUser()` 可能返回 `null`。

**解決方案**:
1. **立即檢查**: 先檢查當前用戶狀態
2. **延遲檢查**: 如果未找到用戶，延遲 2 秒後再次檢查
3. **狀態監聽**: 依賴認證狀態變化監聽器處理後續登入

## 🔄 執行流程

### 應用啟動流程

1. **應用啟動** (`main.dart`)
   - 初始化 Flutter 綁定
   - 快速啟動服務初始化
   - 應用初始化服務（包含設備信息服務初始化）

2. **認證檢查** (`AuthViewModel._initializeAuth()`)
   - 檢查本地儲存的用戶狀態
   - 如果發現已登入用戶：
     - 設置認證狀態為已認證
     - **觸發設備信息更新** ⭐

3. **設備信息更新** (`UserDeviceTrackingService`)
   - 獲取當前平台信息
   - 獲取公網 IP 地址
   - 更新 Firestore 中的 user_profiles 集合

### 用戶登入流程

1. **用戶登入** (任何登入方法)
   - Firebase 認證狀態變化
   - `AuthViewModel` 監聽到狀態變化

2. **認證狀態更新** (`AuthViewModel` 監聽器)
   - 更新內部認證狀態
   - **觸發設備信息更新** ⭐

3. **設備信息更新** (`UserDeviceTrackingService.onUserLogin()`)
   - 強制更新設備信息
   - 更新登入次數和最後登入時間

## 🧪 測試方法

### 測試場景 1：應用啟動時已登入

**步驟**:
1. 確保用戶已登入應用
2. 完全關閉應用
3. 重新啟動應用
4. 檢查 Firestore 中的 user_profiles 集合

**預期結果**:
- 用戶的 `last_login_ip` 欄位應該更新為當前 IP
- 用戶的 `platform` 欄位應該更新為當前平台信息
- 用戶的 `updated_at` 欄位應該更新為當前時間

### 測試場景 2：用戶重新登入

**步驟**:
1. 用戶登出應用
2. 用戶重新登入
3. 檢查 Firestore 中的 user_profiles 集合

**預期結果**:
- 用戶的設備信息應該更新
- 用戶的 `login_count` 應該增加
- 用戶的 `last_login_at` 應該更新

### 測試場景 3：不同平台測試

**步驟**:
1. 在不同平台（Web、Android、iOS）上登入同一用戶
2. 檢查每次登入後的設備信息更新

**預期結果**:
- 平台信息應該正確反映當前使用的平台
- IP 地址應該根據網路環境更新

## 📊 日誌監控

### 關鍵日誌信息

**應用啟動時（立即檢查到用戶）**:
```
[INFO] 初始化認證狀態
[INFO] 發現已登入用戶: [用戶ID]
[INFO] 應用啟動時更新用戶設備信息: [用戶ID]
[INFO] 應用啟動時設備信息更新完成: [用戶ID]
```

**應用啟動時（延遲檢查到用戶）**:
```
[INFO] 初始化認證狀態
[INFO] 用戶未登入（可能 Firebase 認證服務尚未完成初始化）
[INFO] 延遲檢查發現已登入用戶: [用戶ID]
[INFO] 應用啟動時更新用戶設備信息: [用戶ID]
[INFO] 應用啟動時設備信息更新完成: [用戶ID]
```

**用戶登入時**:
```
[INFO] 用戶已登入: [用戶ID]
[INFO] 用戶登入時更新設備信息: [用戶ID]
[INFO] 用戶登入，更新設備信息: [用戶ID]
```

**設備信息更新**:
```
[INFO] 開始更新用戶設備信息: [用戶ID]
[DEBUG] 獲取平台信息: [平台信息]
[DEBUG] 獲取公網 IP 地址成功: [IP地址] (來源: [服務])
[INFO] 用戶設備信息更新成功: [用戶ID]
```

### 錯誤日誌

**設備信息獲取失敗**:
```
[ERROR] 獲取公網 IP 地址失敗: [錯誤信息]
[ERROR] 獲取平台信息失敗: [錯誤信息]
```

**資料庫更新失敗**:
```
[ERROR] 用戶設備信息更新失敗: [用戶ID], 錯誤: [錯誤信息]
```

## 🔍 Firestore 資料驗證

### 檢查方法

1. **Firebase Console**
   - 進入 Firestore Database
   - 導航到 `user_profiles` 集合
   - 查看特定用戶文檔

2. **預期欄位**
   ```json
   {
     "user_id": "用戶ID",
     "last_login_ip": "*************",
     "platform": "Web (Chrome)",
     "last_login_at": "2024-01-01T12:00:00Z",
     "login_count": 5,
     "updated_at": "2024-01-01T12:00:00Z"
   }
   ```

### 平台信息格式

- **Web**: `"Web (Chrome)"`, `"Web (Safari)"`, `"Web (Firefox)"`
- **Android**: `"Android 13 (Samsung SM-G998B)"`
- **iOS**: `"iOS 16.5 (iPhone14,2)"`
- **Windows**: `"Windows 11 (Windows 11 Pro)"`
- **macOS**: `"macOS 13.4 (MacBookPro18,1)"`

## ⚡ 性能考量

### 非阻塞設計

1. **應用啟動時**
   - 使用 `Future.microtask()` 確保不阻塞 UI 初始化
   - 設備信息更新在背景執行

2. **用戶登入時**
   - 使用異步處理，不影響登入流程
   - 錯誤不會影響正常的認證流程

### 快取機制

- **IP 地址**: 5 分鐘快取，避免頻繁請求
- **平台信息**: 永久快取，應用生命週期內不變
- **智能更新**: 只有當信息變化時才更新資料庫

## 🛠️ 故障排除

### 常見問題

1. **設備信息未更新**
   - 檢查網路連接
   - 確認 Firebase 權限設置
   - 查看應用日誌中的錯誤信息

2. **IP 地址獲取失敗**
   - 檢查 IP 檢測服務的可用性
   - 確認防火牆設置
   - 查看網路代理設置

3. **平台信息不準確**
   - 確認 `device_info_plus` 套件版本
   - 檢查平台特定的權限

### 調試步驟

1. **啟用詳細日誌**
   ```dart
   logger.level = Level.DEBUG;
   ```

2. **檢查認證狀態**
   ```dart
   final user = AuthService.getCurrentUser();
   print('當前用戶: ${user?.uid}');
   ```

3. **手動觸發更新**
   ```dart
   await UserDeviceTrackingService.updateUserDeviceInfo(userId, forceUpdate: true);
   ```

## 📈 監控指標

### 成功率指標

- 設備信息更新成功率
- IP 地址獲取成功率
- 平台信息獲取成功率

### 性能指標

- 設備信息更新耗時
- 應用啟動時間影響
- 記憶體使用量

## ✅ 驗證清單

- [ ] 應用啟動時檢測已登入用戶
- [ ] 自動觸發設備信息更新
- [ ] 不阻塞 UI 初始化
- [ ] 正確更新 Firestore 資料
- [ ] 錯誤處理正常運作
- [ ] 日誌記錄完整
- [ ] 性能影響最小化
- [ ] 跨平台兼容性

## 🔄 後續改進

1. **批量更新功能** - 為現有用戶批量更新設備信息
2. **統計分析** - 提供設備信息的統計報告
3. **異常監控** - 監控設備信息更新的異常情況
4. **用戶通知** - 在檢測到異常登入時通知用戶
