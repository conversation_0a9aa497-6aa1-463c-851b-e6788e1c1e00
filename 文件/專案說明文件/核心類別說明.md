# Astreal 核心類別說明文件

## 數據模型類別 (Models)

### BirthData - 出生資料模型
**檔案位置**：`lib/models/birth_data.dart`

**功能描述**：
儲存個人出生資料的核心模型，包含所有計算星盤所需的基本資訊。

**主要屬性**：
```dart
class BirthData {
  final String id;                    // 唯一識別碼
  final String name;                  // 姓名
  final DateTime birthDate;           // 出生日期時間
  final String birthPlace;            // 出生地點
  final double latitude;              // 緯度
  final double longitude;             // 經度
  final String? notes;                // 備註
  final ChartCategory category;       // 資料類別
  final String? folderId;            // 所屬資料夾ID
  final List<String> tags;           // 標籤列表
  final bool isFavorite;             // 是否為最愛
  final DateTime? lastAccessedAt;     // 最後訪問時間
  final DateTime createdAt;          // 建立時間
}
```

**核心方法**：
- `fromJson()` / `toJson()`：JSON 序列化支持
- `copyWith()`：不可變更新支持
- `formatDateTime()`：日期時間格式化

### ChartData - 星盤資料模型
**檔案位置**：`lib/models/chart_data.dart`

**功能描述**：
包含完整星盤計算結果的數據容器，支持多種星盤類型。

**主要屬性**：
```dart
class ChartData {
  late ChartType chartType;                    // 星盤類型
  BirthData primaryPerson;                     // 主要人物
  BirthData? secondaryPerson;                  // 次要人物（合盤用）
  DateTime? specificDate;                      // 特定日期
  List<PlanetPosition>? planets;               // 行星位置列表
  HouseCuspData? houses;                       // 宮位資料
  List<AspectInfo>? aspects;                   // 相位列表
  List<PlanetPosition>? arabicPoints;          // 阿拉伯點列表
  List<FirdariaData>? firdariaData;           // 法達期資料
}
```

### PlanetPosition - 行星位置模型
**檔案位置**：`lib/models/planet_position.dart`

**功能描述**：
儲存單一行星的完整天文和占星資訊。

**主要屬性**：
```dart
class PlanetPosition {
  final int id;                        // 行星ID
  final String name;                   // 行星名稱
  final String symbol;                 // 行星符號
  final double longitude;              // 黃經度數
  final double latitude;               // 黃緯度數
  final double distance;               // 距離
  final double longitudeSpeed;         // 黃經速度
  final String sign;                   // 所在星座
  int house;                          // 所在宮位
  final Color color;                  // 顯示顏色
  final PlanetDignity dignity;        // 尊貴力量
  final SolarCondition solarCondition; // 與太陽關係
  final bool isDaytime;               // 是否在日間區域
  final HouseType houseType;          // 宮位類型
  final SectStatus sectStatus;        // 日夜區分狀態
  List<AspectInfo> aspects;           // 相位資訊
}
```

### AspectInfo - 相位資訊模型
**檔案位置**：`lib/models/aspect_info.dart`

**功能描述**：
定義兩個天體之間的角度關係和相位特性。

**主要屬性**：
```dart
class AspectInfo {
  final int planet1Id;                // 第一個行星ID
  final int planet2Id;                // 第二個行星ID
  final String planet1Name;           // 第一個行星名稱
  final String planet2Name;           // 第二個行星名稱
  final double angle;                 // 實際角度
  final String aspectType;            // 相位類型
  final double orb;                   // 容許度
  final bool isApplying;              // 是否為入相位
  final Color color;                  // 相位線顏色
}
```

## 服務類別 (Services)

### AstrologyService - 占星計算核心服務
**檔案位置**：`lib/services/astrology_service.dart`

**功能描述**：
應用程式的占星計算核心，整合 Swiss Ephemeris 引擎，提供所有星盤計算功能。

**核心方法**：
```dart
class AstrologyService {
  // 初始化 Swiss Ephemeris
  Future<void> initialize()
  
  // 計算星盤數據（主要入口方法）
  Future<ChartData> calculateChartData(ChartData chartData, {...})
  
  // 計算行星位置
  Future<List<PlanetPosition>> calculatePlanetPositions(DateTime dateTime, {...})
  
  // 計算宮位
  Future<HouseCuspData?> calculateHouses(DateTime dateTime, double lat, double lng)
  
  // 計算相位
  List<AspectInfo> calculateAspects(List<PlanetPosition> planets, {...})
  
  // 計算阿拉伯點
  Future<List<PlanetPosition>> calculateArabicPoints(DateTime dateTime, {...})
  
  // 計算行星尊貴力量
  PlanetDignity calculatePlanetDignity(PlanetPosition planet)
}
```

**支持的星盤類型**：
- 本命盤 (Natal Chart)
- 行運盤 (Transit Chart)
- 次限推運盤 (Secondary Progression)
- 三限推運盤 (Tertiary Progression)
- 太陽弧推運盤 (Solar Arc Direction)
- 天象盤 (Mundane Chart)
- 合盤 (Synastry)
- 組合盤 (Composite)
- 戴維森盤 (Davison)
- 馬克思盤 (Marks Chart)

### BirthDataService - 出生資料管理服務
**檔案位置**：`lib/services/birth_data_service.dart`

**功能描述**：
統一管理所有出生資料的 CRUD 操作，支持資料夾分類和標籤系統。

**核心方法**：
```dart
class BirthDataService {
  // 基本 CRUD 操作
  Future<List<BirthData>> getAllBirthData()
  Future<void> addBirthData(BirthData birthData)
  Future<void> updateBirthData(BirthData updatedData)
  Future<void> deleteBirthData(String id)
  Future<BirthData?> getBirthDataById(String id)
  
  // 資料夾管理
  Future<List<BirthData>> getBirthDataByFolder(String? folderId)
  Future<void> updateFolderAssignment(String dataId, String? newFolderId)
  
  // 搜索和篩選
  Future<List<BirthData>> searchBirthData(String query)
  Future<List<BirthData>> getBirthDataByCategory(ChartCategory category)
  Future<List<BirthData>> getBirthDataByTags(List<String> tags)
  
  // 最愛和最近訪問
  Future<List<BirthData>> getFavoriteBirthData()
  Future<List<BirthData>> getRecentBirthData({int limit = 10})
  Future<void> updateLastAccessedTime(String id)
}
```

### AIApiService - AI 服務整合
**檔案位置**：`lib/services/ai_api_service.dart`

**功能描述**：
整合多家 AI 服務提供商，提供星盤解讀和自定義分析功能。

**支持的 AI 提供商**：
- OpenAI (GPT-3.5, GPT-4)
- Anthropic (Claude)
- Groq (高速推理)
- Google Gemini

**核心方法**：
```dart
class AIApiService {
  // 獲取星盤解讀
  static Future<String> getChartInterpretation({
    required ChartData chartData,
    required String prompt,
    String? customModelId,
  })
  
  // API 金鑰管理
  static Future<void> setOpenAIApiKey(String apiKey)
  static Future<void> setAnthropicApiKey(String apiKey)
  static Future<void> setGroqApiKey(String apiKey)
  static Future<void> setGeminiApiKey(String apiKey)
  
  // 模型選擇
  static Future<void> setSelectedModel(String modelId)
  static Future<String> getSelectedModel()
  
  // 使用量統計
  static Future<void> recordTokenUsage(String provider, int tokens)
  static Future<Map<String, int>> getTodayTokenUsage()
}
```

### ChartInterpretationService - 星盤解讀服務
**檔案位置**：`lib/services/chart_interpretation_service.dart`

**功能描述**：
提供各種類型的星盤解讀模板和專業分析功能。

**解讀類型**：
```dart
class ChartInterpretationService {
  // 本命盤解讀
  static Future<String> getNatalChartInterpretation(ChartData chartData)
  
  // 合盤解讀
  static Future<String> getSynastryInterpretation(ChartData chartData)
  
  // 行運解讀
  static Future<String> getTransitInterpretation(ChartData chartData)
  
  // 健康分析
  static Future<String> getHealthInterpretation(ChartData chartData)
  
  // 事業分析
  static Future<String> getCareerInterpretation(ChartData chartData)
  
  // 感情分析
  static Future<String> getRelationshipInterpretation(ChartData chartData)
  
  // 自定義解讀
  static Future<String> getCustomInterpretation({
    required ChartData chartData,
    required String customPrompt,
  })
}
```

## 視圖模型類別 (ViewModels)

### HomeViewModel - 首頁視圖模型
**檔案位置**：`lib/viewmodels/home_viewmodel.dart`

**功能描述**：
管理首頁的所有狀態和業務邏輯，包括今日星相、選中人物、行運資訊等。

**主要功能**：
```dart
class HomeViewModel extends ChangeNotifier {
  // 狀態屬性
  List<Map<String, dynamic>> _recentAspects = [];
  BirthData? _selectedPerson;
  List<BirthData> _birthDataList = [];
  Map<String, dynamic> _transitInfo = {};
  SeasonData? _nextSeason;
  
  // 核心方法
  Future<void> loadRecentAspects()           // 載入今日星相
  Future<void> loadBirthDataList()           // 載入出生資料列表
  Future<void> loadTransitInfo()             // 載入行運資訊
  Future<void> loadNextSeason()              // 載入下一個節氣
  void setSelectedPerson(BirthData? person)  // 設定選中人物
  void setLocation(String location, double lat, double lng) // 設定地點
}
```

### ChartViewModel - 星盤視圖模型
**檔案位置**：`lib/viewmodels/chart_viewmodel.dart`

**功能描述**：
管理星盤頁面的所有狀態，包括星盤計算、設定同步、PDF 生成等。

**主要功能**：
```dart
class ChartViewModel extends ChangeNotifier {
  // 核心數據
  late ChartData _chartData;
  late ChartType _chartType;
  ChartSettings? _chartSettings;
  
  // 狀態管理
  bool _isLoading = true;
  bool _isGeneratingPdf = false;
  bool _isSendingEmail = false;
  
  // 核心方法
  Future<void> calculateChart()              // 計算星盤
  Future<void> loadChartSettings()           // 載入星盤設定
  Future<void> generatePDF()                 // 生成 PDF
  Future<void> sendEmail(String email)       // 發送郵件
  void updateChartType(ChartType newType)    // 更新星盤類型
  void updatePrimaryPerson(BirthData person) // 更新主要人物
}
```

### FilesViewModel - 檔案管理視圖模型
**檔案位置**：`lib/viewmodels/files_viewmodel.dart`

**功能描述**：
管理檔案頁面的所有狀態，包括資料列表、搜索篩選、批量操作等。

**主要功能**：
```dart
class FilesViewModel extends ChangeNotifier {
  // 數據狀態
  List<BirthData> _allBirthData = [];
  List<BirthData> _filteredBirthData = [];
  List<BirthData> _selectedItems = [];
  BirthDataFolder? _currentFolder;
  
  // UI 狀態
  bool _isLoading = false;
  bool _isSelectionMode = false;
  String _searchQuery = '';
  
  // 核心方法
  Future<void> loadBirthData()               // 載入出生資料
  Future<void> searchBirthData(String query) // 搜索資料
  Future<void> filterByFolder(String? folderId) // 按資料夾篩選
  Future<void> deleteSelectedItems()         // 刪除選中項目
  Future<void> moveToFolder(String? folderId) // 移動到資料夾
  void toggleSelectionMode()                 // 切換選擇模式
  void toggleItemSelection(BirthData item)   // 切換項目選擇
}
```

## 工具類別 (Utils)

### AstrologyCalculator - 占星計算工具
**檔案位置**：`lib/utils/astrology_calculator.dart`

**功能描述**：
提供占星計算的輔助工具函數和常用計算方法。

**主要功能**：
```dart
class AstrologyCalculator {
  // 相位計算
  static List<AspectInfo> calculateAspects(List<PlanetPosition> planets)
  
  // 角度計算
  static double calculateAngleBetween(double pos1, double pos2)
  static double normalizeAngle(double angle)
  
  // 星座計算
  static String getZodiacSign(double longitude)
  static int getZodiacSignIndex(double longitude)
  
  // 宮位計算
  static int calculateHousePosition(double longitude, HouseCuspData houses)
  
  // 尊貴力量計算
  static PlanetDignity calculateDignity(int planetId, String sign)
}
```

### JulianDateUtils - 儒略日轉換工具
**檔案位置**：`lib/utils/julian_date_utils.dart`

**功能描述**：
處理日期時間與儒略日之間的轉換，支持時區處理。

**主要功能**：
```dart
class JulianDateUtils {
  // 日期轉換
  static Future<double> dateTimeToJulianDay(DateTime dateTime, double lat, double lng)
  static DateTime julianDayToDateTime(double julianDay)
  
  // 時區處理
  static Future<double> getTimezoneOffset(double latitude, double longitude, DateTime dateTime)
  static DateTime convertToUTC(DateTime localTime, double timezoneOffset)
}
```

### LoggerUtils - 日誌工具
**檔案位置**：`lib/utils/logger_utils.dart`

**功能描述**：
統一的日誌管理工具，支持不同級別的日誌輸出。

**主要功能**：
```dart
class LoggerUtils {
  static void debug(String message)
  static void info(String message)
  static void warning(String message)
  static void error(String message, [dynamic error, StackTrace? stackTrace])
}
```

## UI 組件類別

### ChartViewWidget - 星盤顯示組件
**檔案位置**：`lib/widgets/chart_view_widget.dart`

**功能描述**：
負責星盤的視覺化顯示，支持多種顯示模式和交互功能。

### PlanetListWidget - 行星列表組件
**檔案位置**：`lib/widgets/planet_list_widget.dart`

**功能描述**：
以列表形式顯示所有行星的詳細資訊，包括位置、尊貴力量等。

### AspectTableWidget - 相位表格組件
**檔案位置**：`lib/widgets/aspect_table_widget.dart`

**功能描述**：
以表格形式顯示所有相位資訊，支持排序和篩選功能。

## 特殊功能類別

### FinanceViewModel - 理財占星視圖模型
**檔案位置**：`lib/viewmodels/finance_viewmodel.dart`

**功能描述**：
管理理財占星頁面的狀態，包括行星會合分析、投資性格分析等。

**主要功能**：
```dart
class FinanceViewModel extends ChangeNotifier {
  // 會合分析相關
  List<PlanetaryConjunction> _conjunctions = [];
  bool _isLoadingConjunctions = false;

  // 投資分析相關
  String? _investmentPersonality;
  bool _isAnalyzingInvestment = false;

  // 核心方法
  Future<void> searchConjunctions(DateTime start, DateTime end, double lat, double lng)
  Future<void> analyzeInvestmentPersonality(BirthData person)
  Future<void> analyzeConjunctionImpact(PlanetaryConjunction conjunction)
}
```

### AstroCalendarViewModel - 星象日曆視圖模型
**檔案位置**：`lib/viewmodels/astro_calendar_viewmodel.dart`

**功能描述**：
管理星象日曆的狀態，包括月度事件載入、日期選擇、事件篩選等。

**主要功能**：
```dart
class AstroCalendarViewModel extends ChangeNotifier {
  // 日曆狀態
  DateTime _selectedDate = DateTime.now();
  List<AstroEvent> _events = [];
  Map<DateTime, List<AstroEvent>> _eventsByDate = {};

  // 篩選狀態
  Set<AstroEventType> _selectedEventTypes = {};
  bool _showOnlyMajorEvents = false;

  // 核心方法
  Future<void> loadEventsForMonth(DateTime month)
  Future<void> loadEventsForDate(DateTime date)
  void toggleEventTypeFilter(AstroEventType type)
  void setSelectedDate(DateTime date)
}
```

### ConjunctionCacheService - 會合緩存服務
**檔案位置**：`lib/services/conjunction_cache_service.dart`

**功能描述**：
管理行星會合分析結果的緩存，避免重複計算，提升用戶體驗。

**主要功能**：
```dart
class ConjunctionCacheService {
  // 緩存管理
  static Future<void> saveConjunctionResult({
    required double latitude,
    required double longitude,
    required DateTime startDate,
    required DateTime endDate,
    required ConjunctionAnalysisResult result,
  })

  static Future<ConjunctionAnalysisResult?> loadConjunctionResult({
    required double latitude,
    required double longitude,
    required DateTime startDate,
    required DateTime endDate,
  })

  // 緩存維護
  static Future<void> cleanExpiredCache()
  static Future<void> clearAllCache()
  static Future<int> getCacheSize()
}
```

### FolderService - 資料夾管理服務
**檔案位置**：`lib/services/folder_service.dart`

**功能描述**：
管理出生資料的資料夾分類系統，支持自定義資料夾和預設資料夾。

**主要功能**：
```dart
class FolderService {
  // 資料夾 CRUD
  Future<List<BirthDataFolder>> getAllFolders()
  Future<void> createFolder(BirthDataFolder folder)
  Future<void> updateFolder(BirthDataFolder folder)
  Future<void> deleteFolder(String folderId)

  // 預設資料夾
  Future<BirthDataFolder> getAllFolder()
  Future<BirthDataFolder> getRecentFolder()
  Future<BirthDataFolder> getFavoritesFolder()
  Future<BirthDataFolder> getUncategorizedFolder()

  // 統計功能
  Future<int> getFolderItemCount(String? folderId)
  Future<Map<String, int>> getAllFolderCounts()
}
```

## 枚舉類別說明

### ChartType - 星盤類型枚舉
**檔案位置**：`lib/models/chart_type.dart`

**支持的星盤類型**：
```dart
enum ChartType {
  natal,                    // 本命盤
  transit,                  // 行運盤
  secondaryProgression,     // 次限推運盤
  tertiaryProgression,      // 三限推運盤
  solarArcDirection,        // 太陽弧推運盤
  mundane,                  // 天象盤
  synastry,                 // 合盤
  composite,                // 組合盤
  davison,                  // 戴維森盤
  marks,                    // 馬克思盤
  firdaria,                 // 法達期盤
  horary,                   // 卜卦盤
  solarReturn,              // 太陽回歸盤
  lunarReturn,              // 月亮回歸盤
  eclipse,                  // 日月蝕盤
  equinoxSolstice,         // 二分二至盤
}
```

### ChartCategory - 資料類別枚舉
**檔案位置**：`lib/models/chart_category.dart`

**資料分類**：
```dart
enum ChartCategory {
  personal,     // 個人
  family,       // 家人
  friends,      // 朋友
  partners,     // 伴侶
  work,         // 工作
  clients,      // 客戶
  event,        // 事件
  celestial,    // 天象
  other,        // 其他
}
```

### PlanetDignity - 行星尊貴力量枚舉
**檔案位置**：`lib/models/planet_position.dart`

**尊貴力量等級**：
```dart
enum PlanetDignity {
  domicile,     // 廟 - 最強
  exaltation,   // 旺 - 很強
  triplicity,   // 三分 - 中等
  terms,        // 界 - 較弱
  face,         // 十 - 微弱
  peregrine,    // 普通 - 中性
  detriment,    // 陷 - 較弱
  fall,         // 弱 - 最弱
}
```

## 常數類別說明

### AstrologyConstants - 占星學常數
**檔案位置**：`lib/constants/astrology_constants.dart`

**主要常數定義**：
```dart
class AstrologyConstants {
  // 行星 ID 定義
  static const int SUN = 0;
  static const int MOON = 1;
  static const int MERCURY = 2;
  // ... 其他行星

  // 相位角度定義
  static const double CONJUNCTION = 0.0;
  static const double SEXTILE = 60.0;
  static const double SQUARE = 90.0;
  static const double TRINE = 120.0;
  static const double OPPOSITION = 180.0;

  // 容許度設定
  static const Map<String, double> DEFAULT_ORBS = {
    'conjunction': 8.0,
    'opposition': 8.0,
    'trine': 8.0,
    'square': 7.0,
    'sextile': 6.0,
  };

  // 星座名稱
  static const List<String> ZODIAC_SIGNS = [
    '白羊座', '金牛座', '雙子座', '巨蟹座',
    '獅子座', '處女座', '天秤座', '天蠍座',
    '射手座', '摩羯座', '水瓶座', '雙魚座',
  ];
}
```

## 輔助工具類別

### PersonMatcher - 人物匹配工具
**檔案位置**：`lib/utils/person_matcher.dart`

**功能描述**：
提供智能的人物匹配算法，支持模糊匹配和多重匹配策略。

**主要功能**：
```dart
class PersonMatcher {
  // 精確匹配
  static BirthData? findExactMatch(String id, List<BirthData> persons)

  // 模糊匹配
  static List<BirthData> findSimilarPersons(BirthData target, List<BirthData> persons)

  // 智能匹配
  static BirthData? findBestMatch(BirthData target, List<BirthData> persons)

  // 匹配評分
  static double calculateMatchScore(BirthData person1, BirthData person2)
}
```

### DateFormatter - 日期格式化工具
**檔案位置**：`lib/utils/date_formatter.dart`

**功能描述**：
提供統一的日期時間格式化功能，支持多種顯示格式。

**主要功能**：
```dart
class DateFormatter {
  // 標準格式化
  static String formatDateTime(DateTime dateTime)
  static String formatDate(DateTime dateTime)
  static String formatTime(DateTime dateTime)

  // 相對時間
  static String formatRelativeTime(DateTime dateTime)

  // 占星專用格式
  static String formatAstrologyDateTime(DateTime dateTime)
  static String formatDegreeMinute(double degrees)
}
```

### ChartPdfGenerator - PDF 生成工具
**檔案位置**：`lib/utils/chart_pdf_generator.dart`

**功能描述**：
生成專業的星盤 PDF 報告，支持多種模板和自定義選項。

**主要功能**：
```dart
class ChartPdfGenerator {
  // PDF 生成
  static Future<Uint8List> generateChartPDF({
    required ChartData chartData,
    required ChartSettings settings,
    String? customTitle,
    bool includeInterpretation = false,
  })

  // 模板選擇
  static Future<Uint8List> generateWithTemplate({
    required ChartData chartData,
    required PDFTemplate template,
  })

  // 批量生成
  static Future<List<Uint8List>> generateMultipleCharts(List<ChartData> charts)
}
```

## 數據傳輸對象 (DTO)

### ChartCalculationParams - 星盤計算參數
**檔案位置**：`lib/models/chart_calculation_params.dart`

**功能描述**：
封裝星盤計算所需的所有參數，簡化方法調用。

**主要屬性**：
```dart
class ChartCalculationParams {
  final ChartData chartData;
  final Map<int, bool> planetVisibility;
  final Map<String, double> aspectOrbs;
  final bool calculateArabicPoints;
  final String houseSystem;
  final double effectiveLatitude;
  final double effectiveLongitude;

  // 便利方法
  bool isPlanetVisible(int planetId)
  double getAspectOrb(String aspectType)
  DateTime getEffectiveDateTime()
}
```

這些核心類別構成了 Astreal 應用程式的基礎架構，通過清晰的職責分工和良好的接口設計，確保了系統的可維護性和可擴展性。每個類別都有明確的功能定位，支持應用程式的各種專業占星功能需求。
